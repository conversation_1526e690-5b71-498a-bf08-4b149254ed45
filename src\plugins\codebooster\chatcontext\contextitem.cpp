#include "contextitem.h"

#include <QIcon>
#include <QUuid>
#include <QFileIconProvider>
#include <QFileInfoList>

#include "common/codeboostericons.h"
#include "common/codeboosterutils.h"

namespace CodeBooster {

ContextItem::ContextItem() : itemId(QUuid::createUuid().toString())
{

}

ContextItem ContextItem::buildFileContextFromFilePath(const Utils::FilePath &filePath, bool &success)
{
    QString path = filePath.absoluteFilePath().path();

    success = true;
    QString content = Internal::readTextFile(path, success);
    if (success)
    {
        ContextItem item;
        item.name = filePath.fileName();
        item.description = path;
        item.content = content;
        item.type = ContextItem::File;
        item.uri = path;

        return item;
    }

    return ContextItem();
}

QString ContextItem::contextsToString(const QList<ContextItem> &contexts)
{
    if (contexts.isEmpty()) {
        return QString();
    }
    
    QString contextString;
    contextString += "# Context Information\n";
    
    for (int i = 0; i < contexts.size(); ++i) {
        const ContextItem &item = contexts.at(i);
        contextString += item.contextText();
        
        // 在多个上下文项之间添加分隔线
        if (i < contexts.size() - 1) {
            contextString += "---\n";
        }
    }
    
    return contextString;
}

QString ContextItem::messageWithContexts(const QList<ContextItem> &contexts, const QString &message)
{
    QString result;
    
    // 添加上下文信息
    if (!contexts.isEmpty()) {
        result += contextsToString(contexts);
        result += "\n";
    }
    
    // 为用户消息添加markdown格式的标签
    if (!message.isEmpty()) {
        result += "# User Message\n";
        result += message;
        result += "\n";
    }
    
    return result;
}

QIcon ContextItem::icon() const
{
    if ( (File == type) || (Folder == type) )
    {
        // 创建 QFileIconProvider 实例
        QFileIconProvider iconProvider;

        // 获取文件信息
        QFileInfo fileInfo(uri);

        // 获取图标
        QIcon icon = iconProvider.icon(fileInfo);

        return icon;
    }
    else
    {
        return CODEFILE_ICON.icon();
    }

    return QIcon();
}

QString ContextItem::typeName() const
{
    static QMap<ContextType, QString> nameMapping {
        {File,   "文件" },
        {Folder, "文件夹"},
        {Custom, "自定义"}
    };

    return nameMapping.value(type);
}

QString ContextItem::typeDetail() const
{
    if (type == Folder && fileCount > 0) {
        return QString("文件夹(%1)").arg(fileCount);
    }

    static QMap<ContextType, QString> nameMapping {
        {File,   "文件" },
        {Folder, "文件夹"},
        {Custom, "自定义"}
    };

    return nameMapping.value(type);
}

QString ContextItem::contextText() const
{
    QString text;

    if (File == type)
    {
        text += "## File Context\n";
        text += QString("**File Path:** `%1`\n").arg(uri);
        text += QString("**File Name:** `%1`\n").arg(name);
        text += "**File Content:**\n";
        
        // 尝试从文件路径推断语言类型
        QString language = "";
        if (uri.endsWith(".cpp") || uri.endsWith(".cc") || uri.endsWith(".cxx")) {
            language = "cpp";
        } else if (uri.endsWith(".h") || uri.endsWith(".hpp")) {
            language = "cpp";
        } else if (uri.endsWith(".js")) {
            language = "javascript";
        } else if (uri.endsWith(".py")) {
            language = "python";
        } else if (uri.endsWith(".java")) {
            language = "java";
        } else if (uri.endsWith(".xml") || uri.endsWith(".ui")) {
            language = "xml";
        } else if (uri.endsWith(".json")) {
            language = "json";
        } else if (uri.endsWith(".cmake") || uri.endsWith("CMakeLists.txt")) {
            language = "cmake";
        }
        
        text += QString("```%1\n").arg(language);
        text += content;
        text += "\n```\n";

    }
    else if (Folder == type)
    {
        text += "## Folder Context\n";
        text += QString("**Folder Path:** `%1`\n").arg(uri);
        text += QString("**Folder Name:** `%1`\n").arg(name);
        text += "**Folder Structure:**\n";
        text += QString("```\n%1\n```\n").arg(generateTreeText(uri));
        text += "**Folder Content:**\n";
        text += "```\n";
        text += content;
        text += "\n```\n";
    }

    return text;
}

/**
 * @brief ContextItem::tagText 在编辑器中显示的标签
 * @return
 */
QString ContextItem::tagText() const
{
    QString tag = QString(" @%1:%2 ").arg(typeName()).arg(name);
    return tag;
}

void ContextItem::buildSelectiveContent(const QStringList &selectedFilePaths)
{
    if (type != Folder || selectedFilePaths.isEmpty()) {
        return;
    }

    QString newContent;
    int newFileCount = 0;

    // 为每个选中的文件构建内容
    for (const QString &filePath : selectedFilePaths) {
        QFileInfo fileInfo(filePath);
        if (!fileInfo.exists() || !fileInfo.isFile()) {
            continue;
        }

        // 读取文件内容
        bool success = false;
        QString fileContent = Internal::readTextFile(filePath, success);
        if (!success) {
            continue;
        }

        // 添加文件分隔符和路径信息
        if (newFileCount > 0) {
            newContent += "\n" + QString("=").repeated(50) + "\n";
        }

        // 计算相对路径
        QString relativePath = QDir(uri).relativeFilePath(filePath);
        newContent += QString("File: %1\n").arg(relativePath);
        newContent += QString("Full Path: %1\n").arg(filePath);
        newContent += QString("-").repeated(30) + "\n";
        newContent += fileContent;
        newContent += "\n";

        newFileCount++;
    }

    // 更新内容和文件计数
    content = newContent;
    fileCount = newFileCount;
}

/**
 * @brief ContextItem::toJson
 * @return
 */
QJsonObject ContextItem::toJson() const
{
    /*
    {
        "name": "main.cpp",
        "description": "project/main.cpp",
        "content": "#include<....",
        "type": "1",
        "uri": "D:/project/main.cpp",
        "itemId": "1234567890"
    }
    */
    QJsonObject obj;
    obj.insert("name", name);
    obj.insert("description", description);
    obj.insert("content", content);
    obj.insert("type", type);
    obj.insert("uri", uri);
    obj.insert("itemId", itemId);
    obj.insert("fileCount", fileCount);
    obj.insert("treeStructure", treeStructure);

    return obj;
}

void ContextItem::fromJson(const QJsonObject &json)
{
    name = json.value("name").toString();
    description = json.value("description").toString();
    content = json.value("content").toString();
    type = (ContextType)json.value("type").toInt();
    uri = json.value("uri").toString();
    itemId = json.value("itemId").toString();
    fileCount = json.value("fileCount").toInt();
    treeStructure = json.value("treeStructure").toString();
}

QString ContextItem::generateTreeText(const QString &path, int indentLevel) const
{
    QString treeText;
    QDir dir(path);
    QFileInfoList entryList = dir.entryInfoList(QDir::Dirs | QDir::Files | QDir::NoDotAndDotDot);

    // 添加当前文件夹名称到树形结构的顶部
    if (indentLevel == 0) {
        QFileInfo folderInfo(path);
        treeText += "+" + folderInfo.fileName() + "\n";
        indentLevel++;
    }

    for (const QFileInfo &entry : entryList) {
        // 添加缩进
        for (int i = 0; i < indentLevel; ++i) {
            treeText += "    ";
        }

        // 添加条目名称
        treeText += entry.isDir() ? "+" : "-";
        treeText += entry.fileName() + "\n";

        // 如果是文件夹，递归处理子文件夹
        if (entry.isDir()) {
            treeText += generateTreeText(entry.absoluteFilePath(), indentLevel + 1);
        }
    }

    return treeText;
}

} // namespace CodeBooster
