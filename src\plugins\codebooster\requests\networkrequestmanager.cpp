#include "networkrequestmanager.h"

#include <QNetworkRequest>

#include "requests/replyparser.h"
#include "common/codeboosterutils.h"

namespace CodeBooster {
namespace Internal {

NetworkRequestManager::NetworkRequestManager(QObject *parent)
    : <PERSON>O<PERSON>(parent)
    , manager(new QNetworkAccessManager(this))
    , reply(nullptr)
{
    connect(&timeoutTimer, &QTimer::timeout, this, &NetworkRequestManager::onRequestTimeout);
    connect(this, &NetworkRequestManager::errorOccurred, this, [=](const QStringList &errorInfos){
        outputMessages(errorInfos, Error);
    });
}

NetworkRequestManager::~NetworkRequestManager()
{

}


void NetworkRequestManager::sendRequest(ModelParam param, const QJsonArray &messages, bool stream, int timeout)
{
    // 初始化数据
    response.clear();
    if (reply)
        reply->disconnect();
    reply=nullptr;

    // 构建请求消息数据
    QJsonObject responseJson = CodeBoosterSettings::buildRequestParamJson(param, false);
    responseJson.insert("messages", messages);
    QJsonDocument doc(responseJson);
    QByteArray data = doc.toJson(QJsonDocument::JsonFormat::Compact);

    // 创建网络请求
    // NOTE: QUrl必须加括号，否则编译报错：“.setHeader”的左边必须有类/结构/联合
    QNetworkRequest request((QUrl(param.apiUrl)));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("Authorization", QString("Bearer %1").arg(param.apiKey).toUtf8());

    reply = QSharedPointer<QNetworkReply>(manager->post(request, data));
    if (stream)
    {
        connect(reply.get(), &QNetworkReply::readyRead, this, &NetworkRequestManager::handleStreamReceived);
    }
    connect(reply.get(), &QNetworkReply::finished, this, &NetworkRequestManager::replyFinished);

    timeoutTimer.setSingleShot(true);
    timeoutTimer.start(timeout);

    if (CodeBoosterSettings::instance().devMode())
        CodeBooster::Internal::outputMessages({QString::fromLatin1(doc.toJson())});
}

void NetworkRequestManager::cancelRequest()
{
    if (reply)
        reply->disconnect();
    reply = nullptr;

    timeoutTimer.stop();
}

void NetworkRequestManager::replyFinished()
{
    timeoutTimer.stop();
    if (reply == nullptr)
        return;

    // 发送请求结束状态
    emit requestFinished();

    // 获取结果
    if (reply->error() == QNetworkReply::NoError)
    {
        QByteArray qba = reply->readAll();
        reply->disconnect();
        reply = nullptr;

        if (qba.isEmpty())
        {
            emit errorOccurred({"请求结果为空"});
            return;
        }

        QJsonParseError err;
        QJsonDocument doc=QJsonDocument::fromJson(qba,&err);
        if (err.error!=QJsonParseError::NoError)
        {
            emit errorOccurred({"无法解析结果"});
            return;
        }

        // 解析结果
        QJsonObject obj = doc.object();
        QStringList msgs = ReplyParser::getMessagesFromReply(obj, true);

        emit messageReceived(msgs);

        if (CodeBoosterSettings::instance().devMode())
            CodeBooster::Internal::outputMessages({QString::fromLatin1(doc.toJson())});
    }
    // 处理错误
    else
    {
        QStringList errorInfo;
        errorInfo << "请求错误：";

        QVariant statusCode = reply->attribute(QNetworkRequest::HttpStatusCodeAttribute);
        if (statusCode.isValid()) {
            errorInfo << "HTTP status code：" + QString::number(statusCode.toInt());
        }

        errorInfo << QString("Network error code: %1").arg(reply->error());
        errorInfo << QString("Network error string: %1").arg(reply->errorString());

        emit errorOccurred(errorInfo);
    }

}

void NetworkRequestManager::handleStreamReceived()
{
    timeoutTimer.stop();

    QByteArray data = reply->readLine();
    while (data.length() != 0) {
        if (data == "\n") {
            data = reply->readLine();
            continue;
        }

        int index = data.indexOf('{');
        QByteArray jsonData = data.mid(index);
        if (data == "data: [DONE]\n") {
            emit requestFinished();
            break;
        }

        QJsonDocument doc = QJsonDocument::fromJson(jsonData);
        if (doc.isEmpty()) {
            data = reply->readLine();
            continue;
        }

        QJsonObject jsonObject = doc.object();
        QJsonArray choicesArray = jsonObject["choices"].toArray();
        QJsonObject deltaObject = choicesArray[0].toObject()["delta"].toObject();
        if (deltaObject.contains("content")) {
            QString content = deltaObject["content"].toString();
            response += content;
            emit streamReceived(content);
        }
        data = reply->readLine();
    }
}

void NetworkRequestManager::onRequestTimeout()
{
    reply = nullptr;
    reply->disconnect();
    response.clear();

    emit requestTimeout();
}

} // namespace Internal
} // namespace CodeBooster
