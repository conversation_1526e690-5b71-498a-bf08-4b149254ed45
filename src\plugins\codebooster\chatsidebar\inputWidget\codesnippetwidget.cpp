#include "codesnippetwidget.h"

#include <QVBoxLayout>
#include <QFontMetrics>
#include <QScrollBar>

#include <utils/utilsicons.h>

#include "../markdownpreview/notepreviewwidget.h"
#include "../markdownpreview/markdownhtmlconverter.h"
#include "utility/customlinewidget.h"
#include "common/codeboostericons.h"
#include "common/widgettheme.h"
#include "common/codeboosterutils.h"

namespace CodeBooster::Internal{

/********************************************************************
 CodeSnippetWidget
 代码段显示控件
*********************************************************************/
CodeSnippetWidget::CodeSnippetWidget(QWidget *parent) : <PERSON><PERSON><PERSON>e(parent), mIsPinned(false), mStartLine(1), mEndLine(1)
{
    this->setObjectName("CodeSnippetWidget");
    this->setStyleSheet(CB_THEME.SS_InputWidget_CodeSnippet);

    QVBoxLayout *innerLayout = new QVBoxLayout(this);
    innerLayout->setSpacing(0);
    innerLayout->setContentsMargins(0, 0, 0, 0);

    // 初始化工具栏
    {
        mToolBar = new QToolBar(this);
        mToolBar->setObjectName("mToolBar");
        mToolBar->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar);
        mToolBar->installEventFilter(this);

        mFileIcon = new QLabel(this);
        mFileIcon->setFixedWidth(24);
        mFileIcon->setAlignment(Qt::AlignCenter);
        mFileIcon->setPixmap(CODEFILE_ICON.icon().pixmap(QSize(16, 16)));
        mToolBar->addWidget(mFileIcon);

        // 添加文件名称标签
        mFileNameTitle = new QLabel(this);
        mFileNameTitle->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar_Label);
        // 设置最大宽度，防止长文件名挤压按钮
        mFileNameTitle->setMaximumWidth(200);
        mFileNameTitle->setMinimumWidth(50);
        mFileNameTitle->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
        mToolBar->addWidget(mFileNameTitle);

        // 添加弹簧（让按钮靠右对齐）
        QWidget *spacer = new QWidget();
        spacer->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
        mToolBar->addWidget(spacer);

        // 添加置顶按钮
        mActionPin = new QAction(ICON_PIN.icon(), "置顶", this);
        mActionPin->setCheckable(true);
        connect(mActionPin, &QAction::triggered, this, &CodeSnippetWidget::onActionPinTriggered);
        mToolBar->addAction(mActionPin);

        mActionClose = new QAction(Utils::Icons::CLOSE_TOOLBAR.icon(), "关闭", this);
        connect(mActionClose, &QAction::triggered, this, &CodeSnippetWidget::onActionCloseTriggered);
        mToolBar->addAction(mActionClose);

        mActionExpand = new QAction(EXPAND_ICON.icon(), "折叠", this);
        connect(mActionExpand, &QAction::triggered, this, &CodeSnippetWidget::onActionExpandTriggered);
        mToolBar->addAction(mActionExpand);

        innerLayout->addWidget(mToolBar);
    }

    {
        mHorLine = new CustomLineWidget(this, 12);
        innerLayout->addWidget(mHorLine);
    }

    {
        mPreviewWgt = new NotePreviewWidget(this);
        mPreviewWgt->setObjectName("mPreviewWgt");
        mPreviewWgt->disableLineWrap();
        mPreviewWgt->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeMode_PreWgt);

        mPreviewWgt->setHeightMode(NotePreviewWidget::MaxLimit);
        innerLayout->addWidget(mPreviewWgt, 1);
    }

    setMaximumHeight(330);

    this->setLayout(innerLayout);
}

void CodeSnippetWidget::showCodeSnippet(const QString &fileName, const QString &selectedText, int startLine, int endLine)
{
    QString snippet = selectedText;
    snippet.replace(QChar(0x2028), "\n"); // 替换行分隔符 (LS)
    snippet.replace(QChar(0x2029), "\n"); // 替换段落分隔符 (PS)
    snippet = snippet.trimmed();
    if (snippet.isEmpty())
    {
        clear();
        return;
    }

    this->setVisible(true);

    mFileName = fileName;
    mCodeSnippet = snippet;
    mStartLine = startLine;
    mEndLine = endLine;

    // 更新文件名显示（包含省略处理）
    updateFileNameDisplay();

    QString language = languageFromFileSuffix(QString(fileName.section(".", -1)));
    QString codeSnippet = QString("```%1\n%2\n```").arg(language).arg(mCodeSnippet);
    QString htmlStr = MarkdownHtmlConverter::toMarkdownHtml(codeSnippet);
    mPreviewWgt->setHtml(htmlStr);
    if (mPreviewWgt->verticalScrollBar()->isVisible())
    {
        // TODO: 是否可以判断鼠标指针移动的方向来调整滚动条移动到最上方还是最下方？
        mPreviewWgt->verticalScrollBar()->setValue(mPreviewWgt->verticalScrollBar()->maximum());
    }

    mActionExpand->setText("展开");
    onActionExpandTriggered();
}

QString CodeSnippetWidget::codeSnippet() const
{
    if (mCodeSnippet.isEmpty())
        return QString();

    QString codeText;
    // 在代码片段中包含行号信息
    if (mStartLine == mEndLine) {
        codeText += QString("代码 (%1, 第%2行):\n").arg(mFileName).arg(mStartLine);
    } else {
        codeText += QString("代码 (%1, 第%2-%3行):\n").arg(mFileName).arg(mStartLine).arg(mEndLine);
    }
    codeText += QString("```%1\n").arg(languageFromFileSuffix(QString(mFileName.section(".", -1))));
    codeText += mCodeSnippet;
    codeText += QString("\n```");

    return codeText;
}

void CodeSnippetWidget::clear()
{
    mFileName.clear();
    mCodeSnippet.clear();
    mStartLine = 1;
    mEndLine = 1;
    mPreviewWgt->clear();
    mPreviewWgt->setHtml(QString());
    this->setVisible(false);
}

void CodeSnippetWidget::resizeEvent(QResizeEvent *event)
{
    QFrame::resizeEvent(event);
    emit heightChanged(height());
}

void CodeSnippetWidget::updateFileNameDisplay()
{
    if (mFileName.isEmpty())
        return;

    // 构建完整的显示文本
    QString fullText;
    if (mStartLine == mEndLine) {
        fullText = QString("%1 (第%2行)").arg(mFileName).arg(mStartLine);
    } else {
        fullText = QString("%1 (第%2-%3行)").arg(mFileName).arg(mStartLine).arg(mEndLine);
    }

    // 使用QFontMetrics进行简单的文本省略
    QFontMetrics metrics(mFileNameTitle->font());
    int maxWidth = mFileNameTitle->maximumWidth();
    QString displayText = metrics.elidedText(fullText, Qt::ElideMiddle, maxWidth);

    // 设置文本和提示
    mFileNameTitle->setText(displayText);
    mFileNameTitle->setToolTip(fullText);
}

bool CodeSnippetWidget::eventFilter(QObject *watched, QEvent *event)
{
    if (watched == mToolBar)
    {
        if (event->type() == QEvent::HoverEnter)
        {
            this->setCursor(Qt::PointingHandCursor);
            mToolBar->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar_Highlight);
        }
        else if (event->type() == QEvent::HoverLeave)
        {
            mToolBar->setStyleSheet(CB_THEME.SS_MarkdownBlockWidget_CodeToolBar);
        }
        else if (event->type() == QEvent::MouseButtonPress)
        {
            onActionExpandTriggered();
            return true;
        }
    }
    return QFrame::eventFilter(watched, event);
}

void CodeSnippetWidget::setPinned(bool pinned)
{
    if (mIsPinned == pinned)
        return;

    mIsPinned = pinned;
    mActionPin->setChecked(pinned);

    if (pinned) {
        // 置顶时自动折叠
        mActionExpand->setText("展开");
        mActionExpand->setIcon(COLLAPSE_ICON.icon());
        mHorLine->setVisible(false);
        mPreviewWgt->setVisible(false);

        // 更新置顶按钮图标和提示文本
        mActionPin->setIcon(ICON_PINNED.icon());
        mActionPin->setToolTip("取消置顶");
    } else {
        // 更新置顶按钮图标和提示文本
        mActionPin->setIcon(ICON_PIN.icon());
        mActionPin->setToolTip("置顶");
    }

    emit pinStateChanged(pinned);
}

void CodeSnippetWidget::onActionCloseTriggered()
{
    emit closeRequested();
}

void CodeSnippetWidget::onActionExpandTriggered()
{
    if (mActionExpand->text() == "展开")
    {
        mActionExpand->setText("折叠");
        mActionExpand->setIcon(EXPAND_ICON.icon());

        mHorLine->setVisible(true);
        mPreviewWgt->setVisible(true);
    }
    else
    {
        mActionExpand->setText("展开");
        mActionExpand->setIcon(COLLAPSE_ICON.icon());

        mHorLine->setVisible(false);
        mPreviewWgt->setVisible(false);
    }
}

void CodeSnippetWidget::onActionPinTriggered()
{
    setPinned(!mIsPinned);
}

}
