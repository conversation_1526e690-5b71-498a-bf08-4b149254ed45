#include "asksuggestionwgt.h"

#include <QTextLayout>
#include <QLabel>
#include <QJsonDocument>

#include "common/widgettheme.h"
#include "common/codeboosterutils.h"
#include "requests/promptbuilder.h"
#include "requests/networkrequestmanager.h"

namespace CodeBooster {
namespace Internal {

AskSuggestionWgt::AskSuggestionWgt(QWidget *parent)
    : QWidget{parent}
    , mRequestMgr(new NetworkRequestManager(this))
{
    QVBoxLayout *lyt = new QVBoxLayout(this);

    for (int i = 0; i < 3; i++)
    {
        ClickableLabel *label = new ClickableLabel(this);
        label->setWordWrap(true);
        label->setAlignment(Qt::AlignLeft);
        label->setMinimumHeight(24);
        mSuggestionLabels << label;

        connect(label, &ClickableLabel::clicked, this, &AskSuggestionWgt::labelClicked);

        lyt->addWidget(label);
    }

    lyt->setContentsMargins(2, 2, 2, 2);
    lyt->setSpacing(4);

    connect(mRequestMgr, &NetworkRequestManager::requestTimeout, this, &AskSuggestionWgt::onRequestTimeout);
    connect(mRequestMgr, &NetworkRequestManager::messageReceived, this, &AskSuggestionWgt::onMessageReceived);

    mSpinner = new SpinnerSolution::Spinner(SpinnerSolution::SpinnerSize::Small, this);
    mSpinner->setVisible(false);

    // 为了显示动画
    setMinimumHeight(18);
}

void AskSuggestionWgt::setInactive()
{
    mRequestMgr->cancelRequest();

    clearSuggestion();
    hide();
}

void AskSuggestionWgt::setActive()
{
    show();
}

void AskSuggestionWgt::generateSuggestions(ChatSession session, const ModelParam &param)
{
    clearSuggestion();

    if (!param.isValid())
        return;

    QString question = session.getLastUserMessageContent();
    QString answer = session.getLastAssistantMessageContent();
    if (question.isEmpty() || answer.isEmpty())
    {
        if (CodeBoosterSettings::instance().devMode())
        {
            CodeBooster::Internal::outputMessages({"无法生成问题建议：获取对话信息失败", QString::fromLatin1(QJsonDocument(session.chatStorage()).toJson())});
        }
        return;
    }

    QString userMsg = PromptBuilder::buildGenerateQuestionUserMsg(question, answer);

    ChatSession newSession(PromptBuilder::generateQuestionSystemMsg());
    newSession.appendUserMessage(userMsg);

    QJsonArray messages = newSession.getChatMessage();
    mRequestMgr->sendRequest(param, messages, false, 10000);

    setMinimumHeight(18);
    mSpinner->setVisible(true);
}

void AskSuggestionWgt::labelClicked(QString text)
{
    if (text.isEmpty())
        return;

    suggestionClicked(text);
}

void AskSuggestionWgt::onMessageReceived(const QStringList &msgs)
{
    if (msgs.isEmpty())
        return;

    // 只有第一个是结果
    QString msg = msgs.first();

    QStringList tags{"<QUESTIONS>\n", "\n</QUESTIONS>"};
    for (auto tag : tags)
    {
        msg.remove(tag);
    }

    QStringList suggestions = msg.split("\n");
    if (suggestions.isEmpty())
    {
        return;
    }

    // 显示问题建议
    for (auto label : mSuggestionLabels)
    {
        QString text = suggestions.takeFirst().trimmed();
        if (text.isEmpty())
        {
            continue;
        }
        label->setText(text);
        label->show();

        if (suggestions.isEmpty())
            break;
    }

    emit suggestionCreated();
    mSpinner->setVisible(false);

    // 防止控件大小被压缩
    {
        int minHeightValue = 0;
        for (auto l : mSuggestionLabels)
        {
            if (l->isVisible()) minHeightValue += 30;
        }
        if (minHeightValue == 0) minHeightValue = 18;
        setMinimumHeight(minHeightValue);
    }
}

void AskSuggestionWgt::onRequestTimeout()
{
    outputMessages({"获取问题建议请求超时"}, Error);
    mSpinner->setVisible(false);

    emit generateTimeout();
}

void AskSuggestionWgt::clearSuggestion()
{
    for (auto sugLabel : mSuggestionLabels)
    {
        sugLabel->clear();
        sugLabel->hide();
    }

    mSpinner->setVisible(false);
}


//----------------------------------------------------------------
// ClickableLabel
//----------------------------------------------------------------
ClickableLabel::ClickableLabel(QWidget* parent)
    : QLabel(parent)
{
    // 设置鼠标指针为手指样式
    setCursor(Qt::PointingHandCursor);

    if (CB_SETTING.isDarkTheme())
    {
        mStyleSheet      = "background: #545556; padding: 4px; border-radius: 4px; border: 1px solid #5e5f60; ";
        mHoverStyleSheet = "background: #545556; padding: 4px; border-radius: 4px; border: 1px solid #909090; color: #00e7e7";
    }
    else
    {
        mStyleSheet      = "background: #e9e9e9; padding: 4px; border-radius: 4px; border: 1px solid #e2e2e2; ";
        mHoverStyleSheet = "background: #e9e9e9; padding: 4px; border-radius: 4px; border: 1px solid #cdcdcd; color: #0360c0";
    }

    setStyleSheet(mStyleSheet);
}

ClickableLabel::~ClickableLabel()
{
}

void ClickableLabel::setText(const QString &text)
{
    // 有必要吗？
    //setToolTip(text);
    QLabel::setText(text);
}

void ClickableLabel::enterEvent(QEnterEvent *event)
{
    setHoverStyle(true);  // 鼠标进入时显示边框
    QLabel::enterEvent(event);
}

void ClickableLabel::leaveEvent(QEvent* event)
{
    setHoverStyle(false);  // 鼠标离开时移除边框
    QLabel::leaveEvent(event);
}

void ClickableLabel::mouseReleaseEvent(QMouseEvent* event)
{
    if (event->button() == Qt::LeftButton) {
        emit clicked(text());  // 发送点击信号
    }
    QLabel::mouseReleaseEvent(event);
}

void ClickableLabel::setHoverStyle(bool hover)
{
    if (hover) {
        setStyleSheet(mHoverStyleSheet);
    } else {
        setStyleSheet(mStyleSheet);
    }
}

} // namespace Internal
} // namespace CodeBooster
