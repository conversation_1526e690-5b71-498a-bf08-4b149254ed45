#include "replyparser.h"

#include <QJsonArray>
#include <QJsonObject>

#include "requests/promptbuilder.h"

namespace CodeBooster::Internal {
ReplyParser::ReplyParser() {}

/**
 * @brief ReplyParser::getMessagesFromReply 从回复中解析消息
 * @param model
 * @param reply
 * @param removeStopCode
 * @return
 */
QStringList ReplyParser::getMessagesFromReply(const QJsonObject &reply,
                                              bool removeStopCode)
{
    QStringList msgs;

    // OpenAI 兼容格式
    /* reply示例
        {
          "created": 1703487403,
          "id": "8239375684858666781",
          "model": "glm-4",
          "request_id": "8239375684858666781",
          "choices": [
              {
                  "finish_reason": "stop",
                  "index": 0,
                  "message": {
                      "content": "智绘蓝图，AI驱动 —— 智谱AI，让每一刻创新成为可能。",
                      "role": "assistant"
                  }
              }
          ],
          "usage": {
              "completion_tokens": 217,
              "prompt_tokens": 31,
              "total_tokens": 248
          }
        }
         */

    QJsonArray choices = reply["choices"].toArray();
    for (const QJsonValue &choice : choices)
    {
        msgs.append(getContentFromChoice(choice, removeStopCode));
    }

    return msgs;
}

QString ReplyParser::chatCompletionPostProcess(QString reply)
{
    // Note: 有的模型比较傻，非要输出markdown标签，这里手动处理
    QStringList markdonwTags{"```cpp\n", "\n```", "```\n"};
    for (auto tag : markdonwTags)
    {
        reply.remove(tag);
    }

    return reply;
}

QString ReplyParser::chopStopCode(const QString &content)
{
    // 防止出现这种情况：\n\n<COMPLETION>codeEditor</COMPLETION>
    QString result = content.trimmed();

    for (const auto &code : PromptBuilder::stopCodes())
    {
        if (result.size() > code.size())
        {
            // 将内容开头的停止符移除
            if (result.left(code.size()) == code)
            {
                result = result.mid(code.size());
            }
            // 将内容结尾的停止符移除
            else if (result.right(code.size()) == code)
            {
                result = result.left(result.size() - code.size());
            }
        }
    }

    return result;
}

QString ReplyParser::getContentFromChoice(const QJsonValue &choice, bool removeStopCode)
{
    QJsonObject choiceObj = choice.toObject();
    QString content;

    // 使用流式传输时返回的结果里面message被delta替换
    /*
    data: {"id":"069b737af3019f51e48dc46746192d98","choices":[{"index":0,"delta":{"content":"","role":"assistant"},"finish_reason":null,"logprobs":null}],"created":1717901403,"model":"deepseek-coder","system_fingerprint":"fp_ded2115e5a","object":"chat.completion.chunk","usage":null}
    data: [DONE]
     */

    if (choiceObj.contains("message"))
    {
        QJsonObject message = choiceObj["message"].toObject();
        content = message["content"].toString();
        if (removeStopCode)
            content = chopStopCode(content);
    }
    else if (choiceObj.contains("delta"))
    {
        QJsonObject delta = choiceObj["delta"].toObject();
        content = delta["content"].toString();
        if (removeStopCode)
            content = chopStopCode(content);
    }

    // 去除开头的空格
    content = content.trimmed();

    return content;
}


} // namespace CodeBooster::Internal
