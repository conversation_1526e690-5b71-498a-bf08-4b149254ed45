#ifndef CHATVIEW_H
#define CHATVIEW_H

#include <QWidget>
#include <QNetworkReply>
#include <QVBoxLayout>

#include "./inputWidget/inputwidget.h"
#include "pluginsettings/codeboostersettings.h"
#include "database/chatdatabase.h"
#include "markdownpreview/messagepreviewwidget.h"

namespace Ui {
class ChatView;
}

class SliderIndicator;

namespace CodeBooster::Internal{

class InputWidget;
class ChatHistoryPage;
class AskSuggestionWgt;
class ContextBuilderWgt;

class ChatView : public QWidget
{
    Q_OBJECT

public:
    explicit ChatView(QWidget *parent = nullptr);
    ~ChatView();

    void newChat();
    void loadChat(const QString &uuid);

    QList<QToolButton *> createToolButtons();

    void onSendUserMessage(const QString &message,
                           const QList<ContextItem> &contexts = QList<ContextItem>());
    void sendUserMessageNoHistory(const QString &sysMsg,
                                  const QString &userMsg,
                                  const QList<ContextItem> &contexts = QList<ContextItem>(),
                                  const QMap<QString, QVariant> &overrideParams = QMap<QString, QVariant>());

    bool isFloating() const;

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
    bool event(QEvent *event) override;
    void keyPressEvent(QKeyEvent* event) override;

private slots: 
    void updateAssistantMessage(const QString &content);

    void handleReplyError();
    void streamReceived();
    void stopStreaming(bool finished = true);
    void requestTimeout();

    void onExportBtnClicked();

    void onActionHistoryTriggered();
    void onActionContextBuilderTriggered();
    void onBtnBackToChatClicked();

private:
    bool canCreateNewRequest(bool showInfo = true);
    void request(const QJsonObject &responseJson, const ModelParam &param);
    void setupTheme();
    MessagePreviewWidget *newMessageWidget(MessagePreviewWidget::MessageMode mode, QString modelTitle = QString());
    void requestBegin();
    void requestFinished();
    void loadModelSettings();
    void showErrInfo(QStringList errInfos) const;
    void clearLayout(QLayout* layout);
    void saveChatSession();
    void showChatPage();
    void clearHistoryPage();

    ModelParam currentModelParam() const;

    void updateStopButtonPos();

signals:
    void toSendUserMessage(const QString &message, const QList<ContextItem> &contexts);

private:
    Ui::ChatView *ui;

    QAction *mActionHistory; ///< 打开对话历史动作
    QAction *mActionShowEditorSelection; ///< 输入控件是否显示编辑器选中文本
    QAction *mActionExport;  ///< 导出对话
    QAction *mActionToolBox;

    InputWidget *mInputWidget;  ///< 消息输入控件
    ChatSession mCurSession; ///< 当前的对话
    QVBoxLayout *mMsgLayout;
    QSpacerItem *mMsgSpacer;

    QPushButton *mStopGenerateButton;

    ChatHistoryPage *mHistoryPage;          ///< 对话历史页
    ContextBuilderWgt *mContextBuilderPage; ///< 上下文构建器页

    QList<MessagePreviewWidget *> mMessageWgts; ///< 显示消息的控件
    MessagePreviewWidget *mCurAssistantMsgWgt;
    AskSuggestionWgt *mAskSuggestionWgt;

    // 网络请求相关
    QNetworkReply* repl = nullptr;
    QNetworkAccessManager* manager;
    QString resp; ///< 当前请求的回复
    bool mRequestRunning;
    QTimer mTimeoutTimer;
    // end

    bool mUserScrolledUpWhileStreaming; ///< 在接受流式传输数据时用户滚动了滚动条
    int previousScrollValue;

    QFile *mLogFile;
    QTextStream *mLogStream;
};


// -------------------------------------------------------------------------
// ChatViewManager
// -------------------------------------------------------------------------
class ChatViewManager
{
public:
    ChatViewManager();
    static ChatViewManager &instance();

    ChatView *getOneView() const;
    void registerView(ChatView * view);
    void unregisterView(ChatView * view);

    QList< ChatView* > allViews() const;

private:
    /// 不管理所有权
    QList< ChatView* > mViews;
};

}// namespace CodeBooster::Internal


#endif // CHATVIEW_H
