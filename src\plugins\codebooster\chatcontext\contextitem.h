#ifndef CONTEXTITEM_H
#define CONTEXTITEM_H

#include <QString>
#include <QJsonObject>

#include "utils/filepath.h"

namespace CodeBooster {

class ContextItem
{
public:
    ContextItem();

    static ContextItem buildFileContextFromFilePath(const Utils::FilePath &filePath, bool &success);
    static QString contextsToString(const QList<ContextItem> &contexts);
    static QString messageWithContexts(const QList<ContextItem> &contexts, const QString &message);

    enum ContextType
    {
        File = 1,
        Folder = 2,
        Custom = 255
    };

    QString name;
    QString description;
    QString content;
    ContextType    type;
    QString uri;
    QString itemId;
    int fileCount = 0; ///< 文件夹类型时包含的文件数量
    QString treeStructure; ///< 文件夹类型时的树形结构（用于tooltip显示）


    QJsonObject toJson() const;
    void fromJson(const QJsonObject &json);

    QIcon icon() const;
    QString typeName() const;
    QString typeDetail() const;
    QString contextText() const;

    QString tagText() const;

    // 根据选中的文件路径构建文件夹内容
    void buildSelectiveContent(const QStringList &selectedFilePaths);

private:
    QString generateTreeText(const QString &path, int indentLevel = 0) const;
};

} // namespace CodeBooster

#endif // CONTEXTITEM_H
