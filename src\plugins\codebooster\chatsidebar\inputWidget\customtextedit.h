#ifndef CUSTOMTEXTEDIT_H
#define CUSTOMTEXTEDIT_H

#include <QPlainTextEdit>
#include <QWidget>
#include <QPaintEvent>
#include <QResizeEvent>
#include <QFocusEvent>
#include <QKeyEvent>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>

#include "chatcontext/contextitem.h"

namespace CodeBooster::Internal {

class GitIgnoreParser;
class CustomTextEdit;

/**
 * @brief The LineNumberArea class 行号区域控件
 */
class LineNumberArea : public QWidget
{
public:
    LineNumberArea(CustomTextEdit *editor);

    QSize sizeHint() const Q_DECL_OVERRIDE;

protected:
    void paintEvent(QPaintEvent *event) Q_DECL_OVERRIDE;

private:
    CustomTextEdit *codeEditor;
};

/**
 * @brief The CustomTextEdit class 自定义的文本输入框
 */
class CustomTextEdit : public QPlainTextEdit
{
    Q_OBJECT

public:
    CustomTextEdit(QWidget *parent = nullptr);
    ~CustomTextEdit() override;

    void setFocusMode(bool enable);
    void lineNumberAreaPaintEvent(QPaintEvent *event);
    int lineNumberAreaWidth();

    void addContextItem(const ContextItem &item);
    void removeContextTag(const ContextItem &context);

protected:
    bool event(QEvent *event) override;
    void paintEvent(QPaintEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void focusInEvent(QFocusEvent *event) override;
    void focusOutEvent(QFocusEvent *event) override;
    void keyPressEvent(QKeyEvent* event) override;
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;

private:
    void setPlaceholderTextVisible(bool visible);
    void processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix = "", bool isLast = true);

private slots:
    void adjustInputEditSize();
    void updateLineNumberAreaWidth(int newBlockCount = 0);
    void highlightCurrentLine(bool highlight);
    void updateLineNumberArea(const QRect &, int dy);

signals:
    void sizeChanged();
    void heightChanged(int height);
    void sendMessage();
    void newChat();
    void focusChange(bool focus);
    void focusModeShortcutPress();
    void newContextItem(const ContextItem &item);

private:
    bool mFocusMode;///< 专注编辑模式
    QWidget *lineNumberArea;

    // 常量
    int mMinInputHeight;
    int mMaxInputHeight;

    struct ColorScheme
    {
        QColor lineNumberAreaBackground;
        QColor lineNumberText;
        QColor highlightLineBackground;
        QColor topSeparator;
    };

    ColorScheme mColorScheme;
    CodeBooster::Internal::GitIgnoreParser *mGitIgnoreParser; ///< .gitignore 格式文件过滤器
};

}

#endif // CUSTOMTEXTEDIT_H
