#include "contextbuilderwgt.h"

#include <QApplication>
#include <QClipboard>
#include <QHeaderView>
#include <QMenu>
#include <QMessageBox>
#include <QSplitter>
#include <QScrollArea>
#include <QTimer>
#include <functional>

#include "common/codeboosterutils.h"
#include "common/codeboostericons.h"
#include "common/widgettheme.h"
#include "pluginsettings/codeboostersettings.h"
#include "utility/gitignoreparser.h"

namespace CodeBooster::Internal {

// -------------------------------------------------------------------------
// DragOverlayWidget 实现
// -------------------------------------------------------------------------

DragOverlayWidget::DragOverlayWidget(QWidget *parent)
    : QWidget(parent)
{
    setAttribute(Qt::WA_TransparentForMouseEvents);
    setStyleSheet(
        "DragOverlayWidget {"
        "    background-color: rgba(128, 128, 128, 0.1);"
        "    border: 2px dashed rgba(128, 128, 128, 0.8);"
        "    border-radius: 8px;"
        "}"
    );
}

void DragOverlayWidget::paintEvent(QPaintEvent *event)
{
    QWidget::paintEvent(event);
    
    QPainter painter(this);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // 获取图标
    QIcon icon = CodeBooster::ICON_ADDCONTEXT.icon();
    QPixmap pixmap = icon.pixmap(icon.actualSize(QSize(256, 256)));
    
    // 计算居中位置
    QRect iconRect;
    iconRect.setSize(pixmap.size());
    iconRect.moveCenter(rect().center());
    iconRect.moveTop(rect().center().y() - pixmap.height() / 2 - 20);
    
    // 绘制图标
    painter.drawPixmap(iconRect, pixmap);
    
    // 绘制文字
    painter.setPen(QColor(128, 128, 128));
    QFont font = painter.font();
    font.setPointSize(14);
    font.setBold(true);
    painter.setFont(font);
    
    QRect textRect = rect();
    textRect.setTop(iconRect.bottom() + 10);
    painter.drawText(textRect, Qt::AlignHCenter | Qt::AlignTop, "将文件拖放到此处以添加到您的消息中");
}

// -------------------------------------------------------------------------
// ContextTreeItem 实现
// -------------------------------------------------------------------------

ContextTreeItem::ContextTreeItem(const ContextItem &context, QTreeWidget *parent)
    : QTreeWidgetItem(parent), mContextItem(context)
{
    setText(0, context.name);
    setIcon(0, context.icon());
    setCheckState(0, Qt::Checked);
    setToolTip(0, context.description);

    // 如果是文件夹类型，显示树形结构
    if (context.type == ContextItem::Folder && !context.treeStructure.isEmpty()) {
        setToolTip(0, context.treeStructure);
    }
}

ContextTreeItem::ContextTreeItem(const ContextItem &context, QTreeWidgetItem *parent)
    : QTreeWidgetItem(parent), mContextItem(context)
{
    setText(0, context.name);
    setIcon(0, context.icon());
    setCheckState(0, Qt::Checked);
    setToolTip(0, context.description);

    // 如果是文件夹类型，显示树形结构
    if (context.type == ContextItem::Folder && !context.treeStructure.isEmpty()) {
        setToolTip(0, context.treeStructure);
    }
}

bool ContextTreeItem::isChecked() const
{
    return checkState(0) == Qt::Checked;
}

void ContextTreeItem::setChecked(bool checked)
{
    setCheckState(0, checked ? Qt::Checked : Qt::Unchecked);
}

// -------------------------------------------------------------------------
// ContextNavigationTree 实现
// -------------------------------------------------------------------------

ContextNavigationTree::ContextNavigationTree(QWidget *parent)
    : QTreeWidget(parent), mGitIgnoreParser(nullptr), mIncludeSubfolders(true)
{
    // 设置基本属性
    setHeaderLabels(QStringList() << "项目" << "操作");
    setRootIsDecorated(true);
    setAlternatingRowColors(true);
    setSelectionMode(QAbstractItemView::ExtendedSelection);
    setDragDropMode(QAbstractItemView::DropOnly);
    setAcceptDrops(true);

    // 设置列宽
    header()->setStretchLastSection(false);
    header()->setSectionResizeMode(0, QHeaderView::Stretch);
    header()->setSectionResizeMode(1, QHeaderView::ResizeToContents);

    // 启用右键菜单
    setContextMenuPolicy(Qt::CustomContextMenu);
    setupContextMenu();
    setupToolBar();

    // 初始化 GitIgnore 解析器
    mGitIgnoreParser = new GitIgnoreParser();

    // 连接信号
    connect(this, &QTreeWidget::itemChanged, this, &ContextNavigationTree::onItemChanged);
    connect(this, &QTreeWidget::customContextMenuRequested, this, &ContextNavigationTree::onCustomContextMenuRequested);
}

ContextNavigationTree::~ContextNavigationTree()
{
    delete mGitIgnoreParser;
}

void ContextNavigationTree::setupContextMenu()
{
    mDeleteAction = new QAction("删除", this);
    mDeleteAction->setIcon(QIcon(":/icons/delete.png"));
    connect(mDeleteAction, &QAction::triggered, this, [this]() {
        QList<QTreeWidgetItem*> selectedItems = this->selectedItems();
        for (QTreeWidgetItem *item : selectedItems) {
            delete item;
        }
        emit contextItemsChanged();
    });

    mToggleCheckAction = new QAction("切换勾选状态", this);
    connect(mToggleCheckAction, &QAction::triggered, this, [this]() {
        QList<QTreeWidgetItem*> selectedItems = this->selectedItems();
        for (QTreeWidgetItem *item : selectedItems) {
            ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
            if (contextItem) {
                contextItem->setChecked(!contextItem->isChecked());
            }
        }
        emit contextItemsChanged();
    });
}

void ContextNavigationTree::setupToolBar()
{
    mToolBar = new QToolBar(this);
    mToolBar->setToolButtonStyle(Qt::ToolButtonTextOnly);
    mToolBar->setStyleSheet("QToolBar { border: none; background: transparent; }");

    // 删除所有
    mDeleteAllAction = new QAction("删除所有", this);
    connect(mDeleteAllAction, &QAction::triggered, this, &ContextNavigationTree::clearContextItems);
    mToolBar->addAction(mDeleteAllAction);

    // 全选
    mSelectAllAction = new QAction("全选", this);
    connect(mSelectAllAction, &QAction::triggered, this, &ContextNavigationTree::selectAll);
    mToolBar->addAction(mSelectAllAction);

    // 全不选
    mSelectNoneAction = new QAction("全不选", this);
    connect(mSelectNoneAction, &QAction::triggered, this, &ContextNavigationTree::selectNone);
    mToolBar->addAction(mSelectNoneAction);

    // 反选
    mInvertSelectionAction = new QAction("反选", this);
    connect(mInvertSelectionAction, &QAction::triggered, this, &ContextNavigationTree::invertSelection);
    mToolBar->addAction(mInvertSelectionAction);

    // 刷新
    mRefreshAction = new QAction("刷新", this);
    connect(mRefreshAction, &QAction::triggered, this, &ContextNavigationTree::refreshContextItems);
    mToolBar->addAction(mRefreshAction);

    updateItemActions();
}

void ContextNavigationTree::addContextItem(const ContextItem &context)
{
    // 检查是否已存在相同的项
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *existingItem = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (existingItem && existingItem->contextItem().itemId == context.itemId) {
            // 已存在，更新内容
            existingItem->setContextItem(context);
            existingItem->setText(0, context.name);
            existingItem->setIcon(0, context.icon());
            existingItem->setToolTip(0, context.description);

            // 如果是文件夹且启用了子文件夹，重新构建子项
            if (context.type == ContextItem::Folder && mIncludeSubfolders) {
                // 清除现有子项
                existingItem->takeChildren();
                // 重新添加子项
                buildFolderStructure(existingItem, context.uri);
            }

            updateItemActions();
            emit contextItemsChanged();
            return;
        }
    }

    // 不存在，添加新项
    ContextTreeItem *newItem = new ContextTreeItem(context, this);
    addTopLevelItem(newItem);

    // 在第二列添加删除按钮
    QPushButton *deleteBtn = new QPushButton("删除", this);
    deleteBtn->setMaximumSize(60, 25);
    deleteBtn->setProperty("contextItemId", context.itemId);
    connect(deleteBtn, &QPushButton::clicked, this, &ContextNavigationTree::onDeleteItemClicked);
    setItemWidget(newItem, 1, deleteBtn);

    // 如果是文件夹且启用了子文件夹，构建子项结构
    if (context.type == ContextItem::Folder && mIncludeSubfolders) {
        buildFolderStructure(newItem, context.uri);
        newItem->setExpanded(true); // 默认展开
    }

    updateItemActions();
    emit contextItemsChanged();
}

void ContextNavigationTree::removeContextItem(const QString &itemId)
{
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->contextItem().itemId == itemId) {
            delete takeTopLevelItem(i);
            emit contextItemsChanged();
            return;
        }
    }
}

void ContextNavigationTree::clearContextItems()
{
    clear();
    updateItemActions();
    emit contextItemsChanged();
}

QList<ContextItem> ContextNavigationTree::getSelectedContextItems() const
{
    QList<ContextItem> selectedContexts;

    // 递归收集所有选中的项目
    std::function<void(QTreeWidgetItem*)> collectSelected = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem && contextItem->isChecked()) {
            // 对于子项，需要读取实际的文件内容
            ContextItem context = contextItem->contextItem();

            // 如果是子项（不是顶级项），需要读取文件内容
            if (item->parent() != nullptr && context.type == ContextItem::File) {
                bool success = false;
                QString fileContent = Internal::readTextFile(context.uri, success);
                if (success) {
                    context.content = fileContent;
                }
            }

            selectedContexts.append(context);
        }

        // 递归处理子项
        for (int i = 0; i < item->childCount(); ++i) {
            collectSelected(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        collectSelected(topLevelItem(i));
    }

    return selectedContexts;
}

QList<ContextItem> ContextNavigationTree::getAllContextItems() const
{
    QList<ContextItem> allContexts;

    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item) {
            allContexts.append(item->contextItem());
        }
    }

    return allContexts;
}

void ContextNavigationTree::setGitIgnoreRules(const QString &rules)
{
    if (mGitIgnoreParser) {
        mGitIgnoreParser->setIgnoreRules(rules);
    }
}

void ContextNavigationTree::setIncludeSubfolders(bool include)
{
    if (mIncludeSubfolders == include) {
        return; // 没有变化，直接返回
    }

    mIncludeSubfolders = include;

    // 重新构建所有文件夹的子项结构
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->contextItem().type == ContextItem::Folder) {
            // 清除现有子项
            item->takeChildren();

            // 如果启用子文件夹，重新构建结构
            if (mIncludeSubfolders) {
                buildFolderStructure(item, item->contextItem().uri);
                item->setExpanded(true);
            }
        }
    }

    emit contextItemsChanged();
}

void ContextNavigationTree::selectAll()
{
    // 递归处理所有项目（包括子项）
    std::function<void(QTreeWidgetItem*)> selectAllRecursive = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem) {
            contextItem->setChecked(true);
        }

        for (int i = 0; i < item->childCount(); ++i) {
            selectAllRecursive(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        selectAllRecursive(topLevelItem(i));
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::selectNone()
{
    // 递归处理所有项目（包括子项）
    std::function<void(QTreeWidgetItem*)> selectNoneRecursive = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem) {
            contextItem->setChecked(false);
        }

        for (int i = 0; i < item->childCount(); ++i) {
            selectNoneRecursive(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        selectNoneRecursive(topLevelItem(i));
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::invertSelection()
{
    // 递归处理所有项目（包括子项）
    std::function<void(QTreeWidgetItem*)> invertRecursive = [&](QTreeWidgetItem* item) {
        ContextTreeItem *contextItem = dynamic_cast<ContextTreeItem*>(item);
        if (contextItem) {
            contextItem->setChecked(!contextItem->isChecked());
        }

        for (int i = 0; i < item->childCount(); ++i) {
            invertRecursive(item->child(i));
        }
    };

    for (int i = 0; i < topLevelItemCount(); ++i) {
        invertRecursive(topLevelItem(i));
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::refreshContextItems()
{
    // 刷新所有文件夹类型的上下文项
    for (int i = 0; i < topLevelItemCount(); ++i) {
        ContextTreeItem *item = dynamic_cast<ContextTreeItem*>(topLevelItem(i));
        if (item && item->contextItem().type == ContextItem::Folder) {
            ContextItem context = item->contextItem();

            // 重新处理文件夹
            QString content;
            int fileCount = 0;
            QString treeStructure;

            QFileInfo folderInfo(context.uri);
            if (folderInfo.exists() && folderInfo.isDir()) {
                treeStructure += ".\n";
                treeStructure += "└── " + folderInfo.fileName() + "/\n";

                processDirectory(context.uri, content, fileCount, treeStructure, "    ");

                context.content = content;
                context.fileCount = fileCount;
                context.treeStructure = treeStructure;

                item->setContextItem(context);
                item->setToolTip(0, treeStructure);

                // 重新构建文件夹结构
                if (mIncludeSubfolders) {
                    item->takeChildren(); // 清除现有子项
                    buildFolderStructure(item, context.uri);
                    item->setExpanded(true);
                }
            }
        }
    }
    emit contextItemsChanged();
}

void ContextNavigationTree::updateItemActions()
{
    bool hasItems = topLevelItemCount() > 0;

    if (mDeleteAllAction) mDeleteAllAction->setEnabled(hasItems);
    if (mSelectAllAction) mSelectAllAction->setEnabled(hasItems);
    if (mSelectNoneAction) mSelectNoneAction->setEnabled(hasItems);
    if (mInvertSelectionAction) mInvertSelectionAction->setEnabled(hasItems);
    if (mRefreshAction) mRefreshAction->setEnabled(hasItems);
}

void ContextNavigationTree::buildFolderStructure(ContextTreeItem *parentItem, const QString &folderPath)
{
    QDir dir(folderPath);
    QStringList entryList = dir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    // 分离文件和目录，并排序
    QStringList files, directories;
    for (const QString &entry : entryList) {
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);

        if (fileInfo.isFile()) {
            // 检查文件是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreFile(fullPath, folderPath)) {
                continue;
            }
            if (!fileIsTextFile(fullPath)) {
                continue;
            }
            files.append(entry);
        } else if (fileInfo.isDir()) {
            // 检查目录是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreDirectory(fullPath, folderPath)) {
                continue;
            }
            directories.append(entry);
        }
    }

    // 排序
    files.sort();
    directories.sort();

    // 先添加目录
    for (const QString &dirName : directories) {
        QString fullPath = dir.filePath(dirName);

        // 创建虚拟的ContextItem用于显示
        ContextItem dirContext;
        dirContext.name = dirName;
        dirContext.type = ContextItem::Folder;
        dirContext.uri = fullPath;
        dirContext.description = fullPath;
        dirContext.itemId = QUuid::createUuid().toString();

        ContextTreeItem *dirItem = new ContextTreeItem(dirContext, parentItem);

        // 递归添加子目录内容
        buildFolderStructure(dirItem, fullPath);
    }

    // 再添加文件
    for (const QString &fileName : files) {
        QString fullPath = dir.filePath(fileName);

        // 创建虚拟的ContextItem用于显示
        ContextItem fileContext;
        fileContext.name = fileName;
        fileContext.type = ContextItem::File;
        fileContext.uri = fullPath;
        fileContext.description = fullPath;
        fileContext.itemId = QUuid::createUuid().toString();

        ContextTreeItem *fileItem = new ContextTreeItem(fileContext, parentItem);
    }
}

void ContextNavigationTree::dragEnterEvent(QDragEnterEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir()) {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile()) {
                    if (fileIsTextFile(path)) {
                        hasValidFiles = true;
                    } else {
                        // 发现非文本文件，拒绝整个拖拽操作
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles) {
            event->acceptProposedAction();
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void ContextNavigationTree::dragMoveEvent(QDragMoveEvent *event)
{
    // 检查拖拽数据是否包含URL
    if (event->mimeData()->hasUrls()) {
        QList<QUrl> urlList = event->mimeData()->urls();
        bool hasValidFiles = false;

        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 如果是文件夹，接受拖拽
                if (fileInfo.isDir()) {
                    hasValidFiles = true;
                    continue;
                }

                // 如果是文件，检查是否为文本文件
                if (fileInfo.isFile()) {
                    if (fileIsTextFile(path)) {
                        hasValidFiles = true;
                    } else {
                        // 发现非文本文件，拒绝拖拽
                        event->ignore();
                        return;
                    }
                }
            }
        }

        if (hasValidFiles) {
            event->acceptProposedAction();
        } else {
            event->ignore();
        }
    } else {
        event->ignore();
    }
}

void ContextNavigationTree::dropEvent(QDropEvent *event)
{
    // 获取拖拽进来的数据
    const QMimeData *mimeData = event->mimeData();

    // 处理拖拽的URL数据
    if (mimeData->hasUrls()) {
        QList<QUrl> urlList = mimeData->urls();
        for (const QUrl &url : urlList) {
            if (!url.isLocalFile())
                continue;

            QString path = url.toLocalFile();
            QFileInfo fileInfo(path);

            if (fileInfo.exists()) {
                // 拖进来单个文件
                if (fileInfo.isFile()) {
                    // 检查文件是否为文本文件
                    if (!fileIsTextFile(path)) {
                        continue; // 跳过非文本文件
                    }

                    bool success = false;
                    ContextItem item = ContextItem::buildFileContextFromFilePath(Utils::FilePath::fromString(path), success);
                    if (success) {
                        addContextItem(item);
                    }
                }
                // 拖进来文件夹
                else if (fileInfo.isDir()) {
                    QString content;
                    int fileCount = 0;
                    QString treeStructure;

                    // 添加根目录到树形结构，使用点作为根标识
                    treeStructure += ".\n";
                    treeStructure += "└── " + fileInfo.fileName() + "/\n";

                    processDirectory(path, content, fileCount, treeStructure, "    ");

                    ContextItem item;
                    item.name = fileInfo.fileName();
                    item.description = fileInfo.path();
                    item.content = content;
                    item.type = ContextItem::Folder;
                    item.uri = path;
                    item.fileCount = fileCount;
                    item.treeStructure = treeStructure;

                    addContextItem(item);
                }
            }
        }
    }

    // 接受拖拽事件
    event->accept();
}

// 拖拽事件的公共接口函数实现
void ContextNavigationTree::handleDragEnterEvent(QDragEnterEvent *event)
{
    dragEnterEvent(event);
}

void ContextNavigationTree::handleDragMoveEvent(QDragMoveEvent *event)
{
    dragMoveEvent(event);
}

void ContextNavigationTree::handleDropEvent(QDropEvent *event)
{
    dropEvent(event);
}

void ContextNavigationTree::onItemChanged(QTreeWidgetItem *item, int column)
{
    Q_UNUSED(column)
    Q_UNUSED(item)
    emit contextItemsChanged();
}

void ContextNavigationTree::onCustomContextMenuRequested(const QPoint &pos)
{
    QTreeWidgetItem *item = itemAt(pos);
    if (!item) {
        return;
    }

    QMenu contextMenu(this);
    contextMenu.addAction(mDeleteAction);
    contextMenu.addAction(mToggleCheckAction);

    contextMenu.exec(mapToGlobal(pos));
}

void ContextNavigationTree::onDeleteItemClicked()
{
    QPushButton *button = qobject_cast<QPushButton*>(sender());
    if (!button) return;

    QString itemId = button->property("contextItemId").toString();
    removeContextItem(itemId);
}

// 递归处理文件夹的函数（复用CustomTextEdit的逻辑）
void ContextNavigationTree::processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix, bool isLast)
{
    QDir dir(path);
    QStringList entryList = dir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    // 分离文件和目录，并排序
    QStringList files, directories;
    for (const QString &entry : entryList) {
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);

        if (fileInfo.isFile()) {
            // 检查文件是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreFile(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略文件: %1").arg(fullPath));
                continue;
            }
            if (!fileIsTextFile(fullPath)) {
                continue;
            }
            files.append(entry);
        } else if (fileInfo.isDir()) {
            // 检查目录是否应该被忽略
            if (mGitIgnoreParser && mGitIgnoreParser->shouldIgnoreDirectory(fullPath, path)) {
                devLog(QString("[GitIgnore] 忽略目录: %1").arg(fullPath));
                continue;
            }
            directories.append(entry);
        }
    }

    // 排序
    files.sort();
    directories.sort();

    // 合并列表，目录在前，文件在后
    QStringList allEntries = directories + files;

    for (int i = 0; i < allEntries.size(); ++i) {
        const QString &entry = allEntries.at(i);
        QString fullPath = dir.filePath(entry);
        QFileInfo fileInfo(fullPath);
        bool isLastEntry = (i == allEntries.size() - 1);

        // 构建树形结构字符串
        QString connector = isLastEntry ? "└── " : "├── ";
        treeStructure += prefix + connector + entry;

        if (fileInfo.isDir()) {
            treeStructure += "/\n";

            // 只有在启用子文件夹时才递归处理
            if (mIncludeSubfolders) {
                QString nextPrefix = prefix + (isLastEntry ? "    " : "│   ");
                processDirectory(fullPath, content, fileCount, treeStructure, nextPrefix, isLastEntry);
            }
        } else {
            treeStructure += "\n";

            // 读取文件内容
            bool success = false;
            QString fileContent = Internal::readTextFile(fullPath, success);
            if (success) {
                content += QString("=== %1 ===\n").arg(entry);
                content += fileContent;
                content += "\n\n";
                fileCount++;
            }
        }
    }
}

// -------------------------------------------------------------------------
// ContextBuilderWgt 实现
// -------------------------------------------------------------------------

ContextBuilderWgt::ContextBuilderWgt(QWidget *parent)
    : QWidget(parent)
    , mMainLayout(nullptr)
    , mTabWidget(nullptr)
    , mContextTab(nullptr)
    , mOptionsTab(nullptr)
    , mFilterGroupBox(nullptr)
    , mIncludeSubfoldersYes(nullptr)
    , mIncludeSubfoldersNo(nullptr)
    , mFilterRulesEdit(nullptr)
    , mResetFilterRulesBtn(nullptr)
    , mApplyFilterRulesBtn(nullptr)
    , mContextGroupBox(nullptr)
    , mContextTree(nullptr)
    , mInstructionGroupBox(nullptr)
    , mInstructionEdit(nullptr)
    , mPromptGroupBox(nullptr)
    , mFinalPromptEdit(nullptr)
    , mSendToChatBtn(nullptr)
    , mUpdatePromptTimer(nullptr)
    , mGitIgnoreParser(nullptr)
    , mDragOverlay(nullptr)
{
    setAcceptDrops(true);
    setupUI();
    setupConnections();
    createDragOverlay();

    // 初始化 GitIgnore 解析器
    mGitIgnoreParser = new GitIgnoreParser();

    // 初始化定时器
    mUpdatePromptTimer = new QTimer(this);
    mUpdatePromptTimer->setSingleShot(true);
    mUpdatePromptTimer->setInterval(2000); // 2秒延迟

    // 初始化默认过滤规则（在UI创建之后）
    if (mFilterRulesEdit) {
        QString defaultRules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
        mFilterRulesEdit->setPlainText(defaultRules);
        onFilterRulesChanged();
    }
}

ContextBuilderWgt::~ContextBuilderWgt()
{
    delete mGitIgnoreParser;
}

void ContextBuilderWgt::setupUI()
{
    mMainLayout = new QVBoxLayout(this);
    mMainLayout->setContentsMargins(2, 0, 2, 0);
    mMainLayout->setSpacing(8);

    // 创建Tab控件
    mTabWidget = new QTabWidget(this);

    // 创建上下文Tab
    setupContextTab();

    // 创建选项Tab
    setupOptionsTab();

    // 添加Tab到主布局
    mMainLayout->addWidget(mTabWidget);
}

void ContextBuilderWgt::setupContextTab()
{
    mContextTab = new QWidget();
    QVBoxLayout *contextTabLayout = new QVBoxLayout(mContextTab);
    contextTabLayout->setContentsMargins(8, 8, 8, 8);
    contextTabLayout->setSpacing(8);

    // 1. 上下文导航树区域
    mContextGroupBox = new QGroupBox("上下文 - 拖拽文件或文件夹到此处添加上下文", this);
    {
        QVBoxLayout *contextLayout = new QVBoxLayout(mContextGroupBox);

        mContextTree = new ContextNavigationTree(this);
        mContextTree->setAcceptDrops(false); // 将事件交给外部控件处理
        mContextTree->setMinimumHeight(200);
        // 设置默认的2px灰色虚线边框
        mContextTree->setStyleSheet("QTreeWidget { border: 2px dashed gray; }");

        // 发送到对话框按钮
        mSendToChatBtn = new QPushButton("发送到对话框", this);
        mSendToChatBtn->setEnabled(false);

        // 为发送按钮创建水平布局
        QHBoxLayout *sendButtonLayout = new QHBoxLayout();
        sendButtonLayout->addStretch(); // 添加弹簧，使按钮右对齐
        sendButtonLayout->addWidget(mSendToChatBtn);

        // 添加工具栏到上下文树的上方
        contextLayout->addWidget(mContextTree->getToolBar());
        contextLayout->addWidget(mContextTree);
        contextLayout->addLayout(sendButtonLayout);
    }

    // 2. 指令输入框
    mInstructionGroupBox = new QGroupBox("指令输入", this);
    {
        QVBoxLayout *instructionLayout = new QVBoxLayout(mInstructionGroupBox);

        mInstructionEdit = new QTextEdit(this);
        mInstructionEdit->setAcceptDrops(false); // 将事件交给外部控件处理
        mInstructionEdit->setMaximumHeight(100);
        mInstructionEdit->setPlaceholderText("输入您的提示词指令...");

        instructionLayout->addWidget(mInstructionEdit);
    }

    // 3. 最终Prompt框
    mPromptGroupBox = new QGroupBox("最终 Prompt", this);
    {
        QVBoxLayout *promptLayout = new QVBoxLayout(mPromptGroupBox);

        mFinalPromptEdit = new QPlainTextEdit(this);
        mFinalPromptEdit->setReadOnly(true);
        mFinalPromptEdit->setMinimumHeight(150);
        mFinalPromptEdit->setPlaceholderText("最终的 Prompt 内容将在这里显示...");

        promptLayout->addWidget(mFinalPromptEdit);

        // 添加复制按钮
        QHBoxLayout *promptButtonLayout = new QHBoxLayout();
        promptButtonLayout->addStretch(); // 添加弹簧，使按钮右对齐
        mCopyPromptBtn = new QPushButton("复制", this);
        mCopyPromptBtn->setToolTip("复制最终Prompt内容到剪切板");
        promptButtonLayout->addWidget(mCopyPromptBtn);
        promptLayout->addLayout(promptButtonLayout);
    }

    // 添加到上下文Tab布局
    contextTabLayout->addWidget(mContextGroupBox, 1); // 给上下文区域更多空间
    contextTabLayout->addWidget(mInstructionGroupBox);
    contextTabLayout->addWidget(mPromptGroupBox, 1); // 给Prompt区域更多空间

    // 添加到TabWidget
    mTabWidget->addTab(mContextTab, "上下文");
}

void ContextBuilderWgt::setupOptionsTab()
{
    mOptionsTab = new QWidget();
    QVBoxLayout *optionsTabLayout = new QVBoxLayout(mOptionsTab);
    optionsTabLayout->setContentsMargins(8, 8, 8, 8);
    optionsTabLayout->setSpacing(2);

    // 文件夹内容过滤选项区域
    mFilterGroupBox = new QGroupBox("文件夹内容过滤选项", this);
    {
        QVBoxLayout *filterLayout = new QVBoxLayout(mFilterGroupBox);

        // 读取子文件夹选项
        QHBoxLayout *subfolderLayout = new QHBoxLayout();
        QLabel *subfolderLabel = new QLabel("读取子文件夹：", this);
        mIncludeSubfoldersYes = new QRadioButton("是", this);
        mIncludeSubfoldersNo = new QRadioButton("否", this);
        mIncludeSubfoldersYes->setChecked(true); // 默认选中

        subfolderLayout->addWidget(subfolderLabel);
        subfolderLayout->addWidget(mIncludeSubfoldersYes);
        subfolderLayout->addWidget(mIncludeSubfoldersNo);
        subfolderLayout->addStretch();

        // 过滤规则输入区域
        QLabel *rulesLabel = new QLabel("忽略规则（.gitignore 格式）：", this);

        mFilterRulesEdit = new QTextEdit(this);
        mFilterRulesEdit->setMaximumHeight(200);
        mFilterRulesEdit->setPlaceholderText("输入 .gitignore 格式的过滤规则，留空使用默认规则...");

        // 按钮区域
        QHBoxLayout *buttonLayout = new QHBoxLayout();
        mResetFilterRulesBtn = new QPushButton("重置为默认", this);
        mApplyFilterRulesBtn = new QPushButton("应用规则", this);

        buttonLayout->addWidget(mResetFilterRulesBtn);
        buttonLayout->addWidget(mApplyFilterRulesBtn);
        buttonLayout->addStretch(1);


        // 提示信息
        QLabel *infoLabel = new QLabel("媒体文件、二进制文件和大于 500KB 的文本文件会被自动忽略", this);
        infoLabel->setStyleSheet("color: #666; font-size: 11px;");

        filterLayout->addLayout(subfolderLayout);
        filterLayout->addWidget(rulesLabel);
        filterLayout->addWidget(mFilterRulesEdit);
        filterLayout->addLayout(buttonLayout);
        filterLayout->addWidget(infoLabel);
        filterLayout->addStretch();
    }

    // 添加到选项Tab布局
    optionsTabLayout->addWidget(mFilterGroupBox);
    optionsTabLayout->addStretch(1); // 添加弹性空间

    // 添加到TabWidget
    mTabWidget->addTab(mOptionsTab, "选项");
}



void ContextBuilderWgt::setupConnections()
{
    // 过滤规则相关
    connect(mIncludeSubfoldersYes, &QRadioButton::toggled, this, &ContextBuilderWgt::onIncludeSubfoldersChanged);
    connect(mIncludeSubfoldersNo, &QRadioButton::toggled, this, &ContextBuilderWgt::onIncludeSubfoldersChanged);

    connect(mResetFilterRulesBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onResetFilterRules);
    connect(mApplyFilterRulesBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onFilterRulesChanged);

    connect(mFilterRulesEdit, &QTextEdit::textChanged, this, &ContextBuilderWgt::onFilterRulesChanged);

    // 指令输入相关
    connect(mInstructionEdit, &QTextEdit::textChanged, this, &ContextBuilderWgt::onInstructionTextChanged);

    // 上下文树相关
    connect(mContextTree, &ContextNavigationTree::contextItemsChanged, this, &ContextBuilderWgt::onContextItemsChanged);

    // 定时器相关
    connect(mUpdatePromptTimer, &QTimer::timeout, this, &ContextBuilderWgt::onUpdatePromptTimer);

    // 按钮相关
    connect(mSendToChatBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onSendToChat);
    connect(mCopyPromptBtn, &QPushButton::clicked, this, &ContextBuilderWgt::onCopyPrompt);
}

void ContextBuilderWgt::addContextItem(const ContextItem &context)
{
    mContextTree->addContextItem(context);
}

void ContextBuilderWgt::clearContextItems()
{
    mContextTree->clearContextItems();
    mInstructionEdit->clear();
    mFinalPromptEdit->clear();
    mContextGroupBox->setTitle("上下文 - 拖拽文件或文件夹到此处添加上下文");
}

void ContextBuilderWgt::onInstructionTextChanged()
{
    // 重启定时器
    mUpdatePromptTimer->stop();
    mUpdatePromptTimer->start();
}

void ContextBuilderWgt::onUpdatePromptTimer()
{
    updateFinalPrompt();
}

void ContextBuilderWgt::onContextItemsChanged()
{
    updateFinalPrompt();

    // 更新上下文组框标题
    QList<ContextItem> allContexts = mContextTree->getAllContextItems();
    if (allContexts.isEmpty()) {
        mContextGroupBox->setTitle("上下文 - 拖拽文件或文件夹到此处添加上下文");
    } else {
        int totalFiles = 0;
        int totalFolders = 0;
        for (const ContextItem &context : allContexts) {
            if (context.type == ContextItem::File) {
                totalFiles++;
            } else if (context.type == ContextItem::Folder) {
                totalFolders++;
                totalFiles += context.fileCount; // 文件夹中的文件数
            }
        }
        mContextGroupBox->setTitle(QString("上下文 - 共 %1 个上下文项（%2 个文件夹，%3 个文件）")
                                  .arg(allContexts.size()).arg(totalFolders).arg(totalFiles));
    }
}

void ContextBuilderWgt::onSendToChat()
{
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();
    if (!selectedContexts.isEmpty()) {
        emit sendContextToChat(selectedContexts);
    }
}

void ContextBuilderWgt::onCopyPrompt()
{
    QString promptText = mFinalPromptEdit->toPlainText();
    if (!promptText.isEmpty()) {
        QClipboard *clipboard = QApplication::clipboard();
        clipboard->setText(promptText);
        
        // 可以添加一个简单的提示，表示复制成功
        mCopyPromptBtn->setText("已复制");
        mCopyPromptBtn->setStyleSheet("color: green;");
        QTimer::singleShot(1000, [this]() {
            mCopyPromptBtn->setText("复制");
            mCopyPromptBtn->setStyleSheet("");
        });
    }
}

void ContextBuilderWgt::onFilterRulesChanged()
{
    // 检查UI是否已初始化
    if (!mFilterRulesEdit) {
        return;
    }

    // 获取过滤规则
    QString rules = mFilterRulesEdit->toPlainText().trimmed();

    // 如果没有自定义规则，使用默认规则
    if (rules.isEmpty()) {
        rules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
    }

    // 更新GitIgnore解析器
    if (mGitIgnoreParser) {
        mGitIgnoreParser->setIgnoreRules(rules);
    }

    // 更新上下文树的GitIgnore解析器
    if (mContextTree) {
        mContextTree->setGitIgnoreRules(rules);
    }
}

void ContextBuilderWgt::onIncludeSubfoldersChanged()
{
    if (!mIncludeSubfoldersYes || !mContextTree) {
        return;
    }

    bool includeSubfolders = mIncludeSubfoldersYes->isChecked();
    mContextTree->setIncludeSubfolders(includeSubfolders);
}

void ContextBuilderWgt::onResetFilterRules()
{
    if (mFilterRulesEdit) {
        QString defaultRules = CodeBoosterSettings::instance().getDefaultGitIgnoreRules();
        mFilterRulesEdit->setPlainText(defaultRules);
        onFilterRulesChanged();
    }
}

void ContextBuilderWgt::updateFinalPrompt()
{
    QString finalPrompt = buildFinalPrompt();
    mFinalPromptEdit->setPlainText(finalPrompt);

    // 更新发送按钮状态
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();
    QString instruction = mInstructionEdit->toPlainText().trimmed();
    mSendToChatBtn->setEnabled(!selectedContexts.isEmpty() || !instruction.isEmpty());
}

QString ContextBuilderWgt::buildFinalPrompt() const
{
    QString instruction = mInstructionEdit->toPlainText().trimmed();
    QList<ContextItem> selectedContexts = mContextTree->getSelectedContextItems();

    if (instruction.isEmpty() && selectedContexts.isEmpty()) {
        return QString();
    }

    QString finalPrompt;

    // 添加上下文信息
    if (!selectedContexts.isEmpty()) {
        finalPrompt += ContextItem::contextsToString(selectedContexts);
        finalPrompt += "\n";
    }

    // 添加用户指令
    if (!instruction.isEmpty()) {
        finalPrompt += "# User Message\n";
        finalPrompt += instruction;
        finalPrompt += "\n";
    }

    return finalPrompt;
}

void ContextBuilderWgt::dragEnterEvent(QDragEnterEvent *event)
{
    // 将拖拽事件转发给ContextNavigationTree处理
    if (mContextTree) {
        mContextTree->handleDragEnterEvent(event);
        // 如果事件被接受，显示拖拽遮罩
        // if (event->isAccepted())
        {
            showDragOverlay();
        }
    } else {
        event->ignore();
    }
}

void ContextBuilderWgt::dragMoveEvent(QDragMoveEvent *event)
{
    // 将拖拽事件转发给ContextNavigationTree处理
    if (mContextTree) {
        mContextTree->handleDragMoveEvent(event);
    } else {
        event->ignore();
    }
}

void ContextBuilderWgt::dropEvent(QDropEvent *event)
{
    // 隐藏拖拽遮罩
    hideDragOverlay();
    
    // 将拖拽事件转发给ContextNavigationTree处理
    if (mContextTree) {
        mContextTree->handleDropEvent(event);
    } else {
        event->ignore();
    }
}

void ContextBuilderWgt::dragLeaveEvent(QDragLeaveEvent *event)
{
    // 隐藏拖拽遮罩
    hideDragOverlay();
    QWidget::dragLeaveEvent(event);
}

void ContextBuilderWgt::createDragOverlay()
{
    mDragOverlay = new DragOverlayWidget(this);
    mDragOverlay->hide();
}

void ContextBuilderWgt::showDragOverlay()
{
    // 修改mContextTree的边框为绿色虚线，保留原有样式
    if (mContextTree) {
        QString currentStyle = mContextTree->styleSheet();
        QString dragStyle = currentStyle + "QTreeWidget { border: 2px dashed green !important; }";
        mContextTree->setStyleSheet(dragStyle);
    }
    
    // 保留原有遮罩代码用于对比
    if (mDragOverlay) {
        mDragOverlay->setGeometry(rect());
        mDragOverlay->raise();
        // mDragOverlay->show(); // 注释掉，不显示遮罩
    }
}

void ContextBuilderWgt::hideDragOverlay()
{
    // 恢复mContextTree的默认边框样式（2px灰色虚线）
    if (mContextTree) {
        QString currentStyle = mContextTree->styleSheet();
        // 移除拖拽时添加的绿色边框样式
        QString cleanStyle = currentStyle.replace("QTreeWidget { border: 2px dashed green !important; }", "");
        // 设置默认的2px灰色虚线边框
        cleanStyle += "QTreeWidget { border: 2px dashed gray; }";
        mContextTree->setStyleSheet(cleanStyle);
    }
    
    // 保留原有遮罩代码用于对比
    if (mDragOverlay) {
        mDragOverlay->hide();
    }
}

} // namespace CodeBooster::Internal
