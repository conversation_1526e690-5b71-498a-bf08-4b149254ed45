#ifndef CHATHISTORYPAGE_H
#define CHATHISTORYPAGE_H

#include <QWidget>
#include <QFrame>
#include <QPushButton>
#include <QMessageBox>

#include <solutions/spinner/spinner.h>

namespace Ui {
class ChatHistoryPage;
}
namespace CodeBooster::Internal{

class ChatSessionWgt;

class ChatHistoryPage : public QWidget
{
    Q_OBJECT

public:
    explicit ChatHistoryPage(const QString &sessionUuid, QWidget *parent = nullptr);
    ~ChatHistoryPage();

    void highlightSession(const QString &uuid);

protected:
    void showEvent(QShowEvent *event) override;
    void hideEvent(QHideEvent *event) override;

signals:
    void loadSessionHistory(const QString &uuid);
    void chatDeleted(const QString &uuid);

private slots:
    void onDeleteChatSession(const QString &uuid);
    void onDeleteAllBtnClicked();
    void onSearchSession();
    void onLoadMoreBtnClicked();

    void onShowMoreResultBtnClicked();
    void onStackedWidgetChanged(int index);

    void onCheckBtnClicked();
    void onSelectAllBtnClicked();
    void onInvertSelectionBtnClicked();
    void onDeleteSelectionBtnClicked();

private:
    void loadSessions();
    void updateSessionCountInfo() const;
    void updateSessionWgtInfo(ChatSessionWgt *wgt);
    void updateResultCountInfo() const;
    void showInfo(const QString &info) const;
    void showSessionWidgets(bool showAll, const QStringList &uuids = QStringList());

    QVBoxLayout *currentLayout() const;

    int  loadedSessionCount(QVBoxLayout *layout) const;
    void cleanSearchResult();

    bool inCheckMode() const;
    void updateSessionCheckMode();

    int showMessageBox(const QString &title, const QString &text, QMessageBox::Icon icon = QMessageBox::Warning);

private:
    Ui::ChatHistoryPage *ui;

    QVBoxLayout *mScrollLayout;
    QPushButton *mBtnLoadMoreSession;

    QVBoxLayout *mResultLayout;
    QPushButton *mBtnLoadMoreResult;

    SpinnerSolution::Spinner *mSearchSpinner{nullptr};
    SpinnerSolution::Spinner *mPageSpinner{nullptr};

    static const int CountPerLoading; ///< 单次加载的数量
};

}
#endif // CHATHISTORYPAGE_H
