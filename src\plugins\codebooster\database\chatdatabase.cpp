#include "chatdatabase.h"

#include <QJsonObject>
#include <QDebug>
#include <QDir>
#include <QSqlQuery>
#include <QSqlError>
#include <QJsonDocument>

#include "common/codeboosterutils.h"
#include "utility/instrumentor.h"


namespace CodeBooster::Internal{

QString ChatDatabase::sessionTableSql = R"(CREATE TABLE IF NOT EXISTS "sessions" (
    "uuid" TEXT NOT NULL UNIQUE,
    "title" TEXT,
    "messages" TEXT,
    "message_source" TEXT,
    "context" TEXT,
    "modified_time" INTEGER DEFAULT 1721828273,
    "delete_flag" INTEGER DEFAULT 0,
    PRIMARY KEY("uuid")
);)";

QString ChatDatabase::editorChatInstTableSql = R"(CREATE TABLE IF NOT EXISTS "instructions" (
    "id" INTEGER PRIMARY KEY,
    "instruction" TEXT NOT NULL,
    "created_time" INTEGER DEFAULT 1721828273
);)";


/**
 * @brief ChatDatabase::ChatDatabase
 */
ChatDatabase::ChatDatabase()
{
    // 加载或创建数据库文件
    QString folderPath = dataFolderPath();
    QString fileName = "sessions.db";
    QString fullPath = folderPath + "/" + fileName;

    // 打开或创建数据库文件
    mDb = QSqlDatabase::addDatabase("QSQLITE");
    mDb.setDatabaseName(fullPath);

    if (!mDb.open())
    {
        outputMessages({"Error: connection with database failed"}, Error);
    }
    else
    {
        outputMessages({"Database: connection ok"}, Sucess);

        // 检查是否需要创建 sessions 表
        {
            QSqlQuery query(mDb);
            if (!query.exec("SELECT 1 FROM sessions LIMIT 1"))
            {
                // 表不存在，创建表
                if (!query.exec(sessionTableSql))
                {
                    outputMessages({QString("Failed to create sessions table: %1").arg(query.lastError().text())}, Error);
                } else
                {
                    outputMessage("Sessions table created successfully", Sucess);
                }
            }
        }

        // 创建instructions表
        {
            QSqlQuery query(mDb);
            if (!query.exec("SELECT 1 FROM instructions LIMIT 1"))
            {
                // 表不存在，创建表
                if (!query.exec(editorChatInstTableSql))
                {
                    outputMessages({QString("Failed to create instructions table: %1").arg(query.lastError().text())}, Error);
                } else
                {
                    outputMessage("instructions table created successfully", Sucess);
                }
            }
        }
    }
}

ChatDatabase::~ChatDatabase()
{

}

ChatDatabase &ChatDatabase::instance()
{
    static ChatDatabase db;
    return db;
}

bool ChatDatabase::saveChatSession(const ChatSession &session, QString &err)
{
    // 生成 SQL 语句
    QString sql = "INSERT OR REPLACE INTO sessions (uuid, title, messages, message_source, context, modified_time, delete_flag) VALUES (:uuid, :title, :messages, :message_source, :context, :modified_time, :delete_flag);";

    // 执行 SQL 语句
    QSqlQuery query(mDb);
    query.prepare(sql);
    query.bindValue(":uuid", session.mUuid);
    query.bindValue(":title", session.mTitle);

    // 将 mChatStorage 转换为 JSON 字符串并绑定
    QJsonDocument chatDoc(session.mChatStorage);
    QString messages = QString::fromUtf8(chatDoc.toJson(QJsonDocument::Compact));
    query.bindValue(":messages", messages);

    // 将 mMessageSourceNames 转换为 JSON 数组字符串并绑定
    QJsonArray sourceArray;
    for (const QString &source : session.mMessageSourceNames) {
        sourceArray.append(source);
    }
    QJsonDocument sourceDoc(sourceArray);
    QString messageSource = QString::fromUtf8(sourceDoc.toJson(QJsonDocument::Compact));
    query.bindValue(":message_source", messageSource);

    query.bindValue(":context", session.allContextToJsonString());

    query.bindValue(":modified_time", session.mModifiedTime);
    query.bindValue(":delete_flag", 0);

    if (query.exec()) {
        //qDebug() << "Session saved successfully.";
        return true;
    } else {
        err = QString("Failed to save session. Error: %1").arg(query.lastError().text());
        outputMessage(sql);
        qDebug() << sql;
        return false;
    }
}

bool ChatDatabase::deleteChatSession(const QString &uuid, QString &err) const
{
    QSqlQuery query(mDb);
    query.prepare("UPDATE sessions SET delete_flag = 1 WHERE uuid = :uuid");
    query.bindValue(":uuid", uuid);

    if (query.exec())
    {
        qDebug() << "Session marked as deleted for uuid:" << uuid;
        return true;
    }
    else
    {
        err = QString("Failed to mark session as deleted for uuid: %1 Error: %2").arg(uuid).arg((query.lastError().text()));
        return false;
    }
}

bool ChatDatabase::loadSessionByUuid(const QString &uuid, ChatSession &session)
{
    QSqlQuery query(mDb);
    query.prepare("SELECT * FROM sessions WHERE uuid = :uuid");
    query.bindValue(":uuid", uuid);

    if (query.exec() && query.next())
    {
        session.mUuid  = uuid;
        session.mTitle = query.value("title").toString();
        session.mModifiedTime = query.value("modified_time").toUInt();

        // 解析 messages 字段
        QString messagesJson = query.value("messages").toString();
        QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
        if (messagesDoc.isArray()) {
            session.mChatStorage = messagesDoc.array();
        }

        // 解析 message_source 字段
        QString messageSourceJson = query.value("message_source").toString();
        QJsonDocument messageSourceDoc = QJsonDocument::fromJson(messageSourceJson.toUtf8());
        if (messageSourceDoc.isArray()) {
            QJsonArray sourceArray = messageSourceDoc.array();
            session.mMessageSourceNames.clear();
            for (const QJsonValue &value : sourceArray) {
                session.mMessageSourceNames.append(value.toString());
            }
        }

        // 解析 context 字段
        session.loadContextsFromJsonString(query.value("context").toString());

        return true;
    }
    else
    {
        mLastError = QString("Failed to load session by uuid: %1").arg(query.lastError().text());
        return false;
    }
}

bool ChatDatabase::deleteAllSessions()
{
    QSqlQuery query(mDb);
    QString updateQuery = "UPDATE sessions SET delete_flag = 1 WHERE delete_flag = 0;";

    if (!query.exec(updateQuery))
    {
        mLastError = QString("Error updating delete_flag: %1" ).arg(query.lastError().text());
        return false;
    }
    else
    {
        return true;
    }
}

QList<ChatSessionBrief> ChatDatabase::loadAllSessions() const
{
    QList<ChatSessionBrief> sessions;

    // 准备并执行查询所有会话的 SQL 语句
    QSqlQuery query(mDb);
    query.prepare("SELECT * FROM sessions WHERE delete_flag = 0 ORDER BY modified_time DESC");

    if (query.exec()) {
        while (query.next()) {
            ChatSessionBrief brief;
            brief.uuid = query.value("uuid").toString();
            brief.title = query.value("title").toString();
            brief.modifiedTime = query.value("modified_time").toLongLong();

            // 解析 messages 字段并计算消息数量
            QString messagesJson = query.value("messages").toString();
            QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
            if (messagesDoc.isArray()) {
                brief.messageCount = messagesDoc.array().size();
            }

            sessions.append(brief);
        }
    } else {
        qDebug() << "Failed to load all sessions. Error:" << query.lastError().text();
    }

    return sessions;
}

/**
 * @brief ChatDatabase::loadSessionsByIndexRange 加载modified_time降序排列后范围内的信息
 * @param start
 * @param end
 * @return
 */
bool ChatDatabase::loadSessionsByIndexRange(int start, int end, QList<ChatSessionBrief> &sessions)
{
    PROFILE_FUNCTION();

    if (start < 0 || end < 0)
        return false;

    if (start > end)
        return false;

    // 准备并执行查询指定范围内会话的 SQL 语句
    QSqlQuery query(mDb);
    query.prepare("SELECT * FROM sessions WHERE delete_flag = 0 ORDER BY modified_time DESC LIMIT :limit OFFSET :offset");
    query.bindValue(":limit", end - start);
    query.bindValue(":offset", start);

    if (query.exec()) {
        while (query.next()) {
            ChatSessionBrief brief;
            brief.uuid = query.value("uuid").toString();
            brief.title = query.value("title").toString();
            brief.modifiedTime = query.value("modified_time").toLongLong();

            // 解析 messages 字段并计算消息数量
            QString messagesJson = query.value("messages").toString();
            QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
            if (messagesDoc.isArray()) {
                brief.messageCount = messagesDoc.array().size();
            }

            sessions.append(brief);
        }

        return true;
    }
    else
    {
        mLastError = QString( "Failed to load sessions by index range. Error:" ).arg(query.lastError().text());
        return false;
    }
}

int ChatDatabase::sessionCount()
{
    int count = 0;

    // 准备并执行查询会话数量的 SQL 语句
    QSqlQuery query(mDb);
    query.prepare("SELECT COUNT(*) FROM sessions WHERE delete_flag = 0");

    if (query.exec() && query.next()) {
        count = query.value(0).toInt();
    } else {
        mLastError = QString("Failed to get session count. Error: %1").arg(query.lastError().text());
    }

    return count;
}

int ChatDatabase::searchResultCount(const QString &keyword)
{
    // 获取符合搜索条件的总结果数量
    QSqlQuery countQuery(mDb);
    countQuery.prepare("SELECT COUNT(*) FROM sessions WHERE delete_flag = 0 AND (LOWER(messages) LIKE LOWER(:keyword) OR LOWER(title) LIKE LOWER(:keyword))");
    countQuery.bindValue(":keyword", QVariant("%" + keyword + "%"));

    if (!countQuery.exec() || !countQuery.next()) {
        mLastError = QString("Failed to get search result count. Error: %1").arg(countQuery.lastError().text());
        return -1;
    }

    return countQuery.value(0).toInt();
}

bool ChatDatabase::searchMessage(const QString &keyword, QStringList &uuids)
{
    QSqlQuery query(mDb);
    query.prepare("SELECT uuid FROM sessions WHERE delete_flag = 0 AND (LOWER(messages) LIKE LOWER(:keyword) OR LOWER(title) LIKE LOWER(:keyword))");
    query.bindValue(":keyword", QVariant("%" + keyword + "%"));

    if (query.exec())
    {
        while (query.next())
        {
            uuids << query.value(0).toString();
        }
        return true;
    }
    else
    {
        mLastError = QString("Search query failed: %1").arg(query.lastError().text());
        return false;
    }
}

/**
 * @brief ChatDatabase::searchMessage
 * @param keyword   查询关键字
 * @param start 返回结果的索引开始位置
 * @param end   返回结果的索引结束位置
 * @param sessions 返回结果信息
 * @return
 */
bool ChatDatabase::searchMessage(const QString &keyword, int start, int end, QList<ChatSessionBrief> &sessions)
{
    PROFILE_FUNCTION();

    if (start < 0 || end < 0)
        return false;

    if (start > end)
        return false;

    // 准备并执行分页查询
    QSqlQuery query(mDb);
    query.prepare("SELECT uuid, title, modified_time, messages FROM sessions WHERE delete_flag = 0 AND (LOWER(messages) LIKE LOWER(:keyword) OR LOWER(title) LIKE LOWER(:keyword)) ORDER BY modified_time DESC LIMIT :limit OFFSET :offset");
    query.bindValue(":keyword", QVariant("%" + keyword + "%"));
    query.bindValue(":limit", end - start);
    query.bindValue(":offset", start);

    if (query.exec())
    {
        while (query.next()) {
            ChatSessionBrief brief;
            brief.uuid = query.value("uuid").toString();
            brief.title = query.value("title").toString();
            brief.modifiedTime = query.value("modified_time").toLongLong();

            // 解析 messages 字段并计算消息数量
            QString messagesJson = query.value("messages").toString();
            QJsonDocument messagesDoc = QJsonDocument::fromJson(messagesJson.toUtf8());
            if (messagesDoc.isArray()) {
                brief.messageCount = messagesDoc.array().size();
            }

            sessions.append(brief);
        }

        return true;
    }
    else
    {
        mLastError = QString("Search query failed: %1").arg(query.lastError().text());
        return false;
    }
}

bool ChatDatabase::saveInstruction(const QString &inst)
{
    // 生成 SQL 语句
    QString sql = "INSERT INTO instructions (instruction, created_time) VALUES (:instruction, :created_time);";

    // 执行 SQL 语句
    QSqlQuery query(mDb);
    query.prepare(sql);
    query.bindValue(":instruction", inst);
    query.bindValue(":created_time", QDateTime::currentDateTime().toSecsSinceEpoch());

    if (query.exec()) {
        qDebug() << "instruction saved successfully.";
        return true;
    } else {
        mLastError = QString("Failed to save instruction. Error: %1").arg(query.lastError().text());
        outputMessage(sql);
        qDebug() << sql;
        return false;
    }
}

QStringList ChatDatabase::latestUniqueInstructions(int count)
{
    QStringList result;

    // 生成 SQL 查询语句，按 created_time 降序排列，并限制结果数量
    QString sql = "SELECT DISTINCT instruction FROM instructions ORDER BY created_time DESC LIMIT :count;";

    // 执行 SQL 查询
    QSqlQuery query(mDb);
    query.prepare(sql);
    query.bindValue(":count", count);

    if (query.exec()) {
        // 遍历查询结果，将 instruction 添加到 QStringList 中
        while (query.next()) {
            QString instruction = query.value("instruction").toString();
            result.append(instruction);
        }
    } else {
        // 查询失败，输出错误信息
        mLastError = QString("Failed to fetch latest unique instructions. Error: %1").arg(query.lastError().text());
        qDebug() << mLastError;
    }

    // 将{4, 3, 2, 1}反转为{1, 2, 3, 4}
    std::reverse(result.begin(), result.end());

    return result;
}

QString ChatDatabase::lastError() const
{
    return mLastError;
}




}
