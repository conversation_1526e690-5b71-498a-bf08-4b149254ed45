#include "historylineedit.h"
#include <QKeyEvent>

HistoryLineEdit::HistoryLineEdit(QWidget *parent) : QLineEdit(parent) {
    historyIndex = -1;

    connect(this, &QLineEdit::textEdited, this, [=](const QString &text){
        inputBuffer = text;
    });
}

void HistoryLineEdit::setHistory(QStringList value)
{
    history = value;
    historyIndex = history.size();
}

void HistoryLineEdit::remeberCurrentText()
{
    QString text = this->text().trimmed();
    if (text.isEmpty())
        return;

    if (!history.isEmpty())
    {
        if (history.contains(text))
            history.removeAll(text);

        history.append(text);
    }
    else
    {
        history.append(text);
    }
}

void HistoryLineEdit::keyPressEvent(QKeyEvent *event) {
    QLineEdit::keyPressEvent(event);

    int key = event->key();
    if (key == Qt::Key_Up) {
        showHistory(-1);
    } else if (key == Qt::Key_Down) {
        showHistory(1);
    } else if (key == Qt::Key_Return || key == Qt::Key_Enter) {
        remeberCurrentText();
        historyIndex = history.size();
    }
}

void HistoryLineEdit::showHistory(int direction) {
    if (history.isEmpty()) {
        return;
    }

    historyIndex += direction;
    if (historyIndex < 0)
    {
        historyIndex = 0;
    }
    else if (historyIndex >= history.size())
    {
        historyIndex = history.size();
        this->setText(inputBuffer);
        return;
    }

    this->setText(history[historyIndex]);

    if ((this->text() == inputBuffer)
        && (historyIndex > 0)
        && (historyIndex < history.size()))
    {
        showHistory(direction);
    }
}
