#pragma once

namespace CodeBooster::Constants {
const char CODEBOOSTER_PROJECT_SETTINGS_ID[] = "CodeBooster.Project.Settings";
const char ENABLE_CODEBOOSTER[] = "CodeBooster.EnableCodeBooster";
const char CODEBOOSTER_USE_GLOBAL_SETTINGS[] = "CodeBooster.UseGlobalSettings";

const char CODEBOOSTER_TOGGLE[] = "CodeBooster.Toggle";
const char CODEBOOSTER_ENABLE[] = "CodeBooster.Enable";
const char CODEBOOSTER_DISABLE[] = "CodeBooster.Disable";
const char CODEBOOSTER_REQUEST_SUGGESTION[] = "CodeBooster.RequestSuggestion";
const char CODEBOOSTER_NEXT_SUGGESTION[] = "CodeBooster.NextSuggestion";
const char CODEBOOSTER_PREVIOUS_SUGGESTION[] = "CodeBooster.PreviousSuggestion";

const char M_CODEBOOSTER_TOOL[] = "QtCreator.Menu.Tools.CodeBooster";

const char CODEBOOSTER_GENERAL_OPTIONS_ID[] = "CodeBooster.General";
const char CODEBOOSTER_GENERAL_OPTIONS_CATEGORY[] = "ZY.CodeBooster";
const char CODEBOOSTER_GENERAL_OPTIONS_DISPLAY_CATEGORY[] = "CodeBooster";

const char CODEBOOSTER_CHAT_VIEW_ID[] = "CodeBooster Chat";
const char OUTPUT_PREFIX[] = "[CodeBooster]";
const char VERSION[] = "0.5.3";
} // namespace CodeBooster::Constants
