#include "chatsession.h"

#include <QJsonObject>
#include <QDebug>
#include <QJsonDocument>
#include <QUuid>
#include <QDateTime>

namespace CodeBooster::Internal{

// -------------------------------------------------------------------------
static QMap<ChatSession::Role, QString> roleStringMap{
    {ChatSession::User,      "user"},
    {ChatSession::Assistant, "assistant"}
};

ChatSession::ChatSession(const QString &sysMsg) :
    mUuid(QUuid::createUuid().toString()),
    mModifiedTime(QDateTime::currentDateTime().toSecsSinceEpoch())
{
    // TODO: 系统prompt从设置中读取
    QString prompt;
    if (sysMsg.isEmpty())
    {
        prompt = "你是一个专业的计算机科学和软件工程专家，可以使用C++时优先使用C++回答问题，你拒绝回答你角色范围外的问题，必须使用中文回答。";
    }
    else
    {
        prompt = sysMsg;
    }

    QJsonObject data;
    mSysMsg.insert("role",  "system");
    mSysMsg.insert("content", prompt);
}

void ChatSession::setChatTitle(const QString &title)
{
    mTitle = title;
}

QJsonArray ChatSession::chatStorage() const
{
    return mChatStorage;
}

QString ChatSession::messageSource(int index) const
{
    if (index < mMessageSourceNames.size())
    {
        return mMessageSourceNames.at(index);
    }

    return "未知";
}

void ChatSession::appendUserMessage(const QString &msg, const QList<ContextItem> &contexts)
{
    if (mTitle.isEmpty())
    {
        setChatTitle(msg);
    }

    QJsonObject msgObj = makeContentObject(User, msg);
    QString id = msgObj["id"].toString();

    mChatStorage.append(msgObj);
    mMessageSourceNames << "user";
    if (!contexts.isEmpty())
    {
        mContexts.insert(id, contexts);
    }
    mModifiedTime = QDateTime::currentDateTime().toSecsSinceEpoch();
}

void ChatSession::appendAssistantMessage(const QString &msg, const QString &model)
{
    mChatStorage.append(makeContentObject(Assistant, msg));

    mMessageSourceNames << model;
    mModifiedTime = QDateTime::currentDateTime().toSecsSinceEpoch();
}

QJsonArray ChatSession::getChatMessage(int maxMessageCount, bool useSysMsg)
{
    if (maxMessageCount <= 0)
    {
        maxMessageCount = 1;
    }

    // 获取最大数量为maxMessageCount的消息
    QJsonArray messages;
    int msgCount = 0;
    for (int index = mChatStorage.count() - 1; index >= 0; index--)
    {
        QJsonObject data = getMessageObjByFromIndex(index);
        messages.prepend(data);

        msgCount++;

        if (msgCount >= maxMessageCount)
            break;
    }

    if (useSysMsg)
        messages.prepend(systemMsg());

    return messages;
}

QJsonArray ChatSession::getChatMessage(const QString &msg, const QList<ContextItem> &contexts, int maxMessageCount)
{
    appendUserMessage(msg, contexts);

    return getChatMessage(maxMessageCount);
}

QJsonArray ChatSession::getChatMessage(const QString &sysMsg, const QString &userMsg, const QList<ContextItem> &contexts)
{
    appendUserMessage(userMsg, contexts);

    // 获取最后一条信息
    QJsonArray messages = getChatMessage(1, false);

    // 插入系统信息
    {
        QJsonObject msgObj;
        msgObj.insert("role", "system");
        msgObj.insert("content", sysMsg);
        messages.append(msgObj);
        messages.prepend(msgObj);
    }

    return messages;
}

QList<ContextItem> ChatSession::getMessageContextsById(QString id)
{
    return mContexts.value(id, QList<ContextItem>());
}

QString ChatSession::getLastAssistantMessageContent() const
{
    for (int index = mChatStorage.count() - 1; index >= 0; index--)
    {
        QJsonValue value = mChatStorage.at(index);
        if (value.isObject())
        {
            auto obj = value.toObject();
            if (obj["role"] == "assistant")
                return obj["content"].toString();
        }
    }

    return QString();
}

QString ChatSession::getLastUserMessageContent() const
{
    for (int index = mChatStorage.count() - 1; index >= 0; index--)
    {
        QJsonValue value = mChatStorage.at(index);
        if (value.isObject())
        {
            auto obj = value.toObject();
            if (obj["role"] == "user")
                return obj["content"].toString();
        }
    }

    return QString();
}

QString ChatSession::readableTime() const
{
    // 将 mModifiedTime 转换为 QDateTime 对象
    QDateTime dateTime;
    dateTime.setSecsSinceEpoch(mModifiedTime);

    // 返回格式化的时间字符串
    return dateTime.toString("yyyy-MM-dd HH:mm:ss");
}

ChatSessionBrief ChatSession::toBrief() const
{
    ChatSessionBrief brief;
    brief.uuid = mUuid;
    brief.title = mTitle;
    brief.modifiedTime = mModifiedTime;
    brief.messageCount = mMessageSourceNames.size();

    return brief;
}

QString ChatSession::readableTime(int timeStamp)
{
    QDateTime dateTime;
    dateTime.setSecsSinceEpoch(timeStamp);
    // 返回格式化的时间字符串
    return dateTime.toString("yyyy-MM-dd HH:mm:ss");
}

QJsonObject ChatSession::systemMsg() const
{
    return mSysMsg;
}

QJsonObject ChatSession::makeContentObject(Role role, const QString &msg) const
{
    QJsonObject data;
    data.insert("role",    roleStringMap.value(role));
    data.insert("content", msg);
    data.insert("id", QUuid::createUuid().toString());
    return data;
}

QJsonObject ChatSession::getMessageObjByFromIndex(int index)
{
    if (index < 0 || index >= mChatStorage.size())
        return QJsonObject();

    QJsonObject data = mChatStorage.at(index).toObject();

    // 添加上下文信息
    {
        QString id = data["id"].toString();
        QList<ContextItem> contextItems = mContexts.value(id, QList<ContextItem>());
        if (!contextItems.isEmpty()) {
            data["content"] = ContextItem::messageWithContexts(contextItems, data["content"].toString());
        }
    }

    // 不发送id信息
    data.remove("id");

    return data;
}

QString ChatSession::allContextToJsonString() const
{
    QJsonArray contextsArray;
    for (QString id : mContexts.keys())
    {
        QJsonObject obj;

        {
            obj.insert("id", id);
        }

        {
            QJsonArray contextItemArray;
            for (auto ci : mContexts.value(id))
            {
                contextItemArray.append(ci.toJson());
            }

            obj.insert("contexts", contextItemArray);
        }

        contextsArray << obj;
    }

    /*
    [
        {
            "id": "msgUuid",
            "contexts":
            [
                {
                    "name": "main.cpp",
                    "description": "project/main.cpp",
                    "content": "#include<....",
                    "type": "1",
                    "uri": "D:/project/main.cpp",
                    "itemId": "1234567890"
                },
                {
                    "name": "main.cpp",
                    "description": "project/main.cpp",
                    "content": "#include<....",
                    "type": "1",
                    "uri": "D:/project/main.cpp",
                    "itemId": "1234567890"
                }
            ]
        }
    ]
    */

    QJsonDocument jsonDoc(contextsArray);
    return QString::fromUtf8(jsonDoc.toJson(QJsonDocument::Compact));
}

void ChatSession::loadContextsFromJsonString(const QString &contexJsonString)
{
    // 将 JSON 字符串转换为 QJsonDocument
    QJsonDocument jsonDoc = QJsonDocument::fromJson(contexJsonString.toUtf8());

    // 检查 JSON 文档是否有效
    if (!jsonDoc.isArray())
    {
        qWarning() << "Invalid JSON format: expected an array";
        return;
    }

    // 获取 JSON 数组
    QJsonArray contextsArray = jsonDoc.array();

    // 遍历 JSON 数组中的每个对象
    for (const QJsonValue &contextValue : contextsArray)
    {
        // 检查当前值是否为对象
        if (!contextValue.isObject())
        {
            qWarning() << "Invalid JSON format: expected an object";
            continue;
        }

        // 获取当前对象
        QJsonObject contextObj = contextValue.toObject();

        // 获取 id 和 contexts 数组
        QString id = contextObj.value("id").toString();
        QJsonArray contextItemArray = contextObj.value("contexts").toArray();

        // 创建一个 QList<ContextItem> 来存储当前 id 对应的 ContextItem 对象
        QList<ContextItem> contextItems;

        // 遍历 contexts 数组中的每个对象
        for (const QJsonValue &contextItemValue : contextItemArray)
        {
            // 检查当前值是否为对象
            if (!contextItemValue.isObject())
            {
                qWarning() << "Invalid JSON format: expected an object";
                continue;
            }

            // 获取当前对象
            QJsonObject contextItemObj = contextItemValue.toObject();

            // 创建一个新的 ContextItem 对象并从 JSON 对象中加载数据
            ContextItem contextItem;
            contextItem.fromJson(contextItemObj);

            // 将 ContextItem 对象添加到列表中
            contextItems.append(contextItem);
        }

        // 将 id 和对应的 ContextItem 列表添加到 mContexts 中
        mContexts.insert(id, contextItems);
    }
}

}
