#ifndef CHATDATABASE_H
#define CHATDATABASE_H

#include <QString>
#include <QJsonArray>
#include <QJsonObject>
#include <QSqlDatabase>

#include "chatsession.h"

#define CHAT_DB ChatDatabase::instance()

namespace CodeBooster::Internal{

class ChatDatabase
{
public:
    ChatDatabase();
    ~ChatDatabase();

public:
    static ChatDatabase &instance();
private:
    static QString sessionTableSql;         ///< 侧边栏对话记录表
    static QString editorChatInstTableSql; ///< 编辑器内对话指令表

public:
    bool saveChatSession(const ChatSession &session, QString &err);
    bool deleteChatSession(const QString &uuid, QString &err) const;
    bool loadSessionByUuid(const QString &uuid, ChatSession &session);
    bool deleteAllSessions();
    QList<ChatSessionBrief> loadAllSessions() const;
    bool loadSessionsByIndexRange(int start, int end, QList<ChatSessionBrief> &sessions);
    int sessionCount();

    int searchResultCount(const QString &keyword);
    bool searchMessage(const QString &keyword, QStringList &uuids);
    bool searchMessage(const QString &keyword, int start, int end, QList<ChatSessionBrief> &sessions);

public:
    bool saveInstruction(const QString &inst);
    QStringList latestUniqueInstructions(int count);

public:
    QString lastError() const;

private:
    QSqlDatabase mDb;
    QString mLastError;
};



}
#endif // CHATDATABASE_H
