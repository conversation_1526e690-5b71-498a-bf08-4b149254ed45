#ifndef CONTEXTBUILDERWGT_H
#define CONTEXTBUILDERWGT_H

#include <QWidget>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QTreeWidget>
#include <QTreeWidgetItem>
#include <QTextEdit>
#include <QPlainTextEdit>
#include <QPushButton>
#include <QLabel>
#include <QGroupBox>
#include <QTimer>
#include <QDragEnterEvent>
#include <QDragMoveEvent>
#include <QDropEvent>
#include <QDragLeaveEvent>
#include <QMimeData>
#include <QUrl>
#include <QFileInfo>
#include <QDir>
#include <QLineEdit>
#include <QRadioButton>
#include <QToolBar>
#include <QAction>
#include <QToolButton>
#include <QTabWidget>
#include <QPainter>
#include <QPixmap>

#include <solutions/spinner/spinner.h>
#include "chatcontext/contextitem.h"

namespace CodeBooster::Internal {

class GitIgnoreParser;

/**
 * @brief 拖拽遮罩层widget，显示拖拽提示
 */
class DragOverlayWidget : public QWidget
{
    Q_OBJECT

public:
    explicit DragOverlayWidget(QWidget *parent = nullptr);

protected:
    void paintEvent(QPaintEvent *event) override;
};

/**
 * @brief 上下文导航树项，用于在树形控件中显示上下文项
 */
class ContextTreeItem : public QTreeWidgetItem
{
public:
    explicit ContextTreeItem(const ContextItem &context, QTreeWidget *parent = nullptr);
    explicit ContextTreeItem(const ContextItem &context, QTreeWidgetItem *parent = nullptr);

    ContextItem contextItem() const { return mContextItem; }
    void setContextItem(const ContextItem &context) { mContextItem = context; }

    bool isChecked() const;
    void setChecked(bool checked);

private:
    ContextItem mContextItem;
};

/**
 * @brief 支持拖拽的上下文导航树
 */
class ContextNavigationTree : public QTreeWidget
{
    Q_OBJECT

public:
    explicit ContextNavigationTree(QWidget *parent = nullptr);
    ~ContextNavigationTree();

    void addContextItem(const ContextItem &context);
    void removeContextItem(const QString &itemId);
    void clearContextItems();

    QList<ContextItem> getSelectedContextItems() const;
    QList<ContextItem> getAllContextItems() const;

    // 获取指定顶层节点的选中子文件路径
    QStringList getSelectedChildFilePaths(ContextTreeItem *topLevelItem) const;

    void setGitIgnoreRules(const QString &rules);
    void setIncludeSubfolders(bool include);

    void selectAll();
    void selectNone();
    void invertSelection();
    void refreshContextItems();

    QToolBar* getToolBar() const { return mToolBar; }
    QLineEdit* getFilterLineEdit() const { return mFilterLineEdit; }
    void setUpFilterLineEdit();

    // 拖拽事件的公共接口函数
    void handleDragEnterEvent(QDragEnterEvent *event);
    void handleDragMoveEvent(QDragMoveEvent *event);
    void handleDropEvent(QDropEvent *event);

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    //void dragLeaveEvent(QDragLeaveEvent *event) override;

private slots:
    void onItemChanged(QTreeWidgetItem *item, int column);
    void onCustomContextMenuRequested(const QPoint &pos);
    void onDeleteItemClicked();

private:
    void setupContextMenu();
    void setupToolBar();
    void processDirectory(const QString &path, QString &content, int &fileCount, QString &treeStructure, const QString &prefix = "", bool isLast = true);
    void updateItemActions();
    void addDeleteButtonToItem(ContextTreeItem *item, const QString &itemId);
    void buildFolderStructure(ContextTreeItem *parentItem, const QString &folderPath);
    void updateChildrenCheckState(QTreeWidgetItem *parentItem, Qt::CheckState state);
    void updateParentCheckState(QTreeWidgetItem *childItem);

signals:
    void contextItemsChanged();

private:
    GitIgnoreParser *mGitIgnoreParser;
    bool mIncludeSubfolders;

    // 工具栏和操作
    QToolBar *mToolBar;
    QAction *mDeleteAllAction;
    QAction *mSelectAllAction;
    QAction *mSelectNoneAction;
    QAction *mInvertSelectionAction;
    QAction *mRefreshAction;

    // 右键菜单
    QAction *mDeleteAction;
    QAction *mToggleCheckAction;

    // 过滤功能
    QLineEdit *mFilterLineEdit;

public slots:
    void onFilterTextChanged();

private:
    void filterTreeItems(const QString &filterText);
};

/**
 * @brief 上下文构建器主控件
 */
class ContextBuilderWgt : public QWidget
{
    Q_OBJECT

public:
    explicit ContextBuilderWgt(QWidget *parent = nullptr);
    ~ContextBuilderWgt();

    void addContextItem(const ContextItem &context);
    void clearContextItems();

private slots:
    void onInstructionTextChanged();
    void onUpdatePromptTimer();
    void onSendToChat();
    void onCopyPrompt();
    void onContextItemsChanged();
    void onFilterRulesChanged();
    void onIncludeSubfoldersChanged();
    void onResetFilterRules();

protected:
    void dragEnterEvent(QDragEnterEvent *event) override;
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dropEvent(QDropEvent *event) override;
    void dragLeaveEvent(QDragLeaveEvent *event);

private:
    void setupUI();
    void setupContextTab();
    void setupOptionsTab();
    void setupConnections();
    void updateFinalPrompt();
    QString buildFinalPrompt() const;

    // 拖拽遮罩相关方法
    void showDragOverlay();
    void hideDragOverlay();
    void createDragOverlay();

signals:
    void sendContextToChat(const QList<ContextItem> &contexts);

private:
    // UI 组件
    QVBoxLayout *mMainLayout;
    QTabWidget *mTabWidget;

    // 上下文Tab
    QWidget *mContextTab;

    // 选项Tab
    QWidget *mOptionsTab;
    QGroupBox *mFilterGroupBox;
    QRadioButton *mIncludeSubfoldersYes;
    QRadioButton *mIncludeSubfoldersNo;
    QTextEdit *mFilterRulesEdit;
    QPushButton *mResetFilterRulesBtn;
    QPushButton *mApplyFilterRulesBtn;

    // 上下文导航树区域
    QGroupBox *mContextGroupBox;
    ContextNavigationTree *mContextTree;

    // 指令输入框
    QGroupBox *mInstructionGroupBox;
    QTextEdit *mInstructionEdit;

    // 最终Prompt框
    QGroupBox *mPromptGroupBox;
    QPlainTextEdit *mFinalPromptEdit;
    QPushButton *mCopyPromptBtn;

    // 操作按钮
    QPushButton *mSendToChatBtn;

    // 定时器
    QTimer *mUpdatePromptTimer;

    // 其他
    GitIgnoreParser *mGitIgnoreParser;

    // 拖拽遮罩层
    DragOverlayWidget *mDragOverlay;

    // Spinner加载指示器
    SpinnerSolution::Spinner *mSpinner;

    static const int UPDATE_PROMPT_TIMEOUT_MS;
};

} // namespace CodeBooster::Internal

#endif // CONTEXTBUILDERWGT_H
